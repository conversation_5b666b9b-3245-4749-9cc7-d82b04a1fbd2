# 需要删除的旧系统API接口清单

## 🚫 货币系统接口 - 全部删除

### 用户资源管理
```
DELETE /api/v1/user/gold                    # 金币相关
DELETE /api/v1/user/diamond                 # 钻石相关  
DELETE /api/v1/user/coins                   # 硬币相关
DELETE /api/v1/user/currency/*              # 所有货币接口
DELETE /api/v1/user/balance                 # 余额查询
DELETE /api/v1/user/wallet                  # 钱包系统
```

### 商店购买系统
```
DELETE /api/v1/shop/*                       # 所有商店接口
DELETE /api/v1/purchase/*                   # 所有购买接口
DELETE /api/v1/store/*                      # 商店相关
DELETE /api/v1/products/*                   # 商品相关
DELETE /api/v1/payment/*                    # 支付相关
DELETE /api/v1/transactions/*               # 交易记录
```

## 🚫 大炮/武器系统接口 - 全部删除

### 大炮管理
```
DELETE /api/v1/cannon/*                     # 所有大炮接口
DELETE /api/v1/cannons/*                    # 大炮列表
DELETE /api/v1/game/cannon/*                # 游戏中大炮接口
DELETE /api/v1/user/cannon/*                # 用户大炮接口
```

### 武器系统
```
DELETE /api/v1/weapons/*                    # 所有武器接口
DELETE /api/v1/user/weapons/*               # 用户武器
DELETE /api/v1/weapon/upgrade               # 武器升级
DELETE /api/v1/weapon/switch                # 武器切换
```

### 战斗系统
```
DELETE /api/v1/combat/*                     # 战斗相关
DELETE /api/v1/battle/*                     # 战役相关
DELETE /api/v1/shooting/*                   # 射击相关
DELETE /api/v1/game/fire                    # 开火接口
DELETE /api/v1/game/attack                  # 攻击接口
```

## 🚫 旧排行榜系统 - 需要替换

### 基于货币的排行榜
```
DELETE /api/v1/ranking/gold                 # 金币排行榜
DELETE /api/v1/ranking/diamond              # 钻石排行榜
DELETE /api/v1/ranking/wealth               # 财富排行榜
DELETE /api/v1/leaderboard/money            # 金钱排行榜
DELETE /api/v1/leaderboard/combat           # 战斗排行榜
DELETE /api/v1/leaderboard/weapons          # 武器排行榜
```

## 🚫 旧统计系统接口

### 战斗统计
```
DELETE /api/v1/stats/combat                 # 战斗统计
DELETE /api/v1/stats/shooting               # 射击统计
DELETE /api/v1/stats/accuracy               # 精确度统计
DELETE /api/v1/analytics/battle             # 战斗分析
```

### 经济统计  
```
DELETE /api/v1/stats/economy                # 经济统计
DELETE /api/v1/stats/spending               # 消费统计
DELETE /api/v1/analytics/purchase           # 购买分析
```

## ✅ 需要修改的现有接口

### 用户资料接口 - 移除货币字段
```
GET /api/v1/user/profile
旧响应字段删除:
- gold, diamond, coins
- current_cannon, cannon_level
- combat_stats, weapon_stats

新响应字段:
- civilization_exp, guardian_level
- stamina, max_stamina
- collections_count
- total_thieves_captured, total_garbage_cleaned
```

### 热点交互接口 - 完全重新设计
```
POST /api/v1/game/hotspot/interact
旧响应删除:
- gold_reward, diamond_reward
- weapon_drops, ammo_reward
- combat_exp

新响应结构:
- civilization_exp_reward
- boss_damage_dealt
- collection_drops
- stamina_consumed
- task_progress_updates
```

### 游戏会话接口 - 移除货币统计
```
POST /api/v1/game/session/start
POST /api/v1/game/session/end
移除字段:
- gold_earned, diamond_spent
- shots_fired, accuracy_rate
- combat_performance

添加字段:
- civilization_exp_earned
- thieves_captured, garbage_cleaned
- monuments_protected
- collections_obtained
```

## 🆕 完全新增的接口系统

### 文明守护者系统
```
GET /api/v1/user/civilization/status        # 文明状态
GET /api/v1/user/civilization/exp-logs      # 经验记录
POST /api/v1/user/guardian/level-up         # 守望者升级
```

### BOSS战斗系统
```
GET /api/v1/boss/status/{city_id}           # BOSS状态
POST /api/v1/boss/attack                    # 攻击BOSS
GET /api/v1/boss/dialogue/{city_id}/{stage} # BOSS对话
```

### 文化图鉴系统
```
GET /api/v1/collections/city/{city_id}      # 城市图鉴
POST /api/v1/collections/collect            # 收集图鉴
GET /api/v1/collections/user/stats          # 收集统计
```

### 古迹问答系统
```
GET /api/v1/monuments/{city_id}             # 古迹列表
POST /api/v1/monuments/{id}/start-challenge # 开始挑战
POST /api/v1/monuments/challenge/{id}/submit # 提交答案
```

### 每日任务系统
```
GET /api/v1/tasks/daily                     # 每日任务
POST /api/v1/tasks/daily/{id}/double-reward # 双倍奖励
POST /api/v1/tasks/progress/update          # 更新进度
```

### 体力系统
```
GET /api/v1/user/stamina/status             # 体力状态
POST /api/v1/user/stamina/consume           # 消耗体力
POST /api/v1/user/stamina/recover           # 恢复体力
```

### 新排行榜系统
```
GET /api/v1/ranking/civilization-exp       # 文明经验排行
GET /api/v1/ranking/collections            # 图鉴收集排行
GET /api/v1/ranking/combined               # 综合排行
```

## 📋 清理执行步骤

1. **数据库清理** - 执行 `000_cleanup_old_system.sql`
2. **后端接口删除** - 删除上述标记的所有旧接口
3. **新接口实现** - 实现新的文明守护者系统接口
4. **前端对接** - 更新前端调用新接口
5. **测试验证** - 确保新系统正常运行

## 🎯 关键改变总结

- **货币系统** → **文明经验值系统**
- **大炮战斗** → **文明守护行动**
- **武器升级** → **守望者等级提升**
- **战斗排行榜** → **文明贡献排行榜**
- **商店购买** → **任务奖励和图鉴收集**
- **弹药系统** → **体力系统**
- **敌人战斗** → **清理坏蛋拯救文明**