# 文明守护者游戏系统 - API接口设计

## 1. 用户文明经验系统接口

### 获取用户文明状态
```
GET /api/v1/user/civilization/status
```
**响应示例：**
```json
{
  "success": true,
  "data": {
    "user_id": 123,
    "civilization_exp": 2500,
    "guardian_level": 5,
    "guardian_level_name": "英勇守卫",
    "stamina": 85,
    "max_stamina": 100,
    "collections_count": 15,
    "total_thieves_captured": 150,
    "total_garbage_cleaned": 200,
    "total_monuments_protected": 8,
    "guardian_avatar_position": {
      "x": 100,
      "y": 200,
      "city_id": "beijing"
    },
    "next_level": {
      "level": 6,
      "required_exp": 4000,
      "progress_percentage": 62.5
    }
  }
}
```

### 获取文明经验记录
```
GET /api/v1/user/civilization/exp-logs
Query参数: limit=20, offset=0, source=all
```

## 2. BOSS系统接口

### 获取BOSS状态
```
GET /api/v1/boss/status/{city_id}
```
**响应示例：**
```json
{
  "success": true,
  "data": {
    "city_id": "beijing",
    "boss_name": "垃圾大王·北京",
    "current_hp": 750,
    "max_hp": 1000,
    "hp_percentage": 75.0,
    "stage": "80",
    "is_defeated": false,
    "total_damage_dealt": 250,
    "next_dialogue_stage": "60",
    "avatar_url": "/bosses/beijing/avatar.png",
    "background_url": "/bosses/beijing/background.jpg"
  }
}
```

### 攻击BOSS
```
POST /api/v1/boss/attack
Content-Type: application/json
```
**请求体：**
```json
{
  "city_id": "beijing",
  "damage_amount": 15,
  "damage_source": "thief",
  "source_id": "thief_001"
}
```
**响应示例：**
```json
{
  "success": true,
  "data": {
    "damage_dealt": 15,
    "boss_hp_before": 750,
    "boss_hp_after": 735,
    "current_stage": "80",
    "triggered_dialogue": false,
    "stage_reward": null,
    "is_defeated": false
  }
}
```

### 获取BOSS对话
```
GET /api/v1/boss/dialogue/{city_id}/{stage}
```
**响应示例：**
```json
{
  "success": true,
  "data": {
    "stage": "60",
    "dialogue_text": "可恶！你这个讨厌的守护者！我愿意分给你一半的垃圾王国，放过我吧！",
    "dialogue_type": "bribe",
    "reward_exp": 15,
    "boss_avatar": "/bosses/beijing/angry_face.png"
  }
}
```

### 重置BOSS（新城市）
```
POST /api/v1/boss/reset/{city_id}
```

## 3. 文化图鉴系统接口

### 获取城市图鉴列表
```
GET /api/v1/collections/city/{city_id}
Query参数: category=all, rarity=all, collected=all
```
**响应示例：**
```json
{
  "success": true,
  "data": {
    "total_count": 20,
    "collected_count": 8,
    "completion_rate": 40.0,
    "collections": [
      {
        "id": 1,
        "name": "北京烤鸭",
        "name_en": "Beijing Roast Duck",
        "description": "北京最著名的传统美食，皮脆肉嫩",
        "category": "food",
        "rarity": "epic",
        "image_url": "/collections/beijing/peking_duck.jpg",
        "exp_reward": 25,
        "is_collected": true,
        "collected_at": "2025-01-15T10:30:00Z",
        "cultural_value": "北京烤鸭是中华美食文化的瑰宝"
      }
    ]
  }
}
```

### 收集图鉴
```
POST /api/v1/collections/collect
Content-Type: application/json
```
**请求体：**
```json
{
  "collection_id": 1,
  "collected_from": "thief",
  "source_location": "hotspot_thief_001"
}
```

### 获取用户收集统计
```
GET /api/v1/collections/user/stats
```

## 4. 古迹问答系统接口

### 获取城市古迹列表
```
GET /api/v1/monuments/{city_id}
```
**响应示例：**
```json
{
  "success": true,
  "data": {
    "monuments": [
      {
        "id": 1,
        "monument_name": "故宫太和殿",
        "monument_name_en": "Hall of Supreme Harmony",
        "location_description": "紫禁城中轴线上最重要的建筑",
        "polluted_image_url": "/monuments/beijing/taihebulding_polluted.jpg",
        "clean_image_url": "/monuments/beijing/taihebulding_clean.jpg",
        "difficulty_level": "medium",
        "success_exp": 60,
        "failure_exp": -15,
        "is_protected": false,
        "success_count": 0,
        "total_attempts": 0
      }
    ]
  }
}
```

### 开始古迹问答挑战
```
POST /api/v1/monuments/{monument_id}/start-challenge
```
**响应示例：**
```json
{
  "success": true,
  "data": {
    "challenge_id": "challenge_12345",
    "monument_id": 1,
    "questions": [
      {
        "id": 1,
        "question_text": "太和殿是紫禁城中轴线上最重要的建筑，它主要用于什么场合？",
        "question_type": "single_choice",
        "options": [
          {
            "id": 1,
            "option_text": "皇帝举行重大典礼",
            "option_order": 1
          },
          {
            "id": 2,
            "option_text": "皇帝日常办公",
            "option_order": 2
          }
        ]
      }
    ],
    "time_limit_seconds": 300
  }
}
```

### 提交答案
```
POST /api/v1/monuments/challenge/{challenge_id}/submit
Content-Type: application/json
```
**请求体：**
```json
{
  "answers": [
    {
      "question_id": 1,
      "selected_option_ids": [1]
    }
  ]
}
```
**响应示例：**
```json
{
  "success": true,
  "data": {
    "questions_total": 3,
    "questions_correct": 2,
    "is_success": false,
    "exp_gained": -10,
    "boss_damage_dealt": 0,
    "time_spent_seconds": 245,
    "correct_answers": [
      {
        "question_id": 1,
        "is_correct": true,
        "explanation": "太和殿是皇帝举行登基、大婚、册封等重大典礼的场所"
      }
    ]
  }
}
```

## 5. 每日任务系统接口

### 获取每日任务列表
```
GET /api/v1/tasks/daily
Query参数: date=2025-01-15
```
**响应示例：**
```json
{
  "success": true,
  "data": {
    "date": "2025-01-15",
    "tasks": [
      {
        "id": 1,
        "task_name": "初级清道夫",
        "task_type": "catch_thieves",
        "description": "抓捕10个小偷，维护城市治安",
        "current_progress": 3,
        "target_count": 10,
        "progress_percentage": 30.0,
        "is_completed": false,
        "exp_reward": 50,
        "is_double_rewarded": false,
        "difficulty": "easy"
      }
    ],
    "completion_stats": {
      "total_tasks": 10,
      "completed_tasks": 4,
      "completion_rate": 40.0,
      "total_exp_available": 500,
      "total_exp_earned": 200
    }
  }
}
```

### 领取双倍奖励（观看广告）
```
POST /api/v1/tasks/daily/{task_id}/double-reward
Content-Type: application/json
```
**请求体：**
```json
{
  "ad_completion_token": "ad_token_12345"
}
```

### 更新任务进度
```
POST /api/v1/tasks/progress/update
Content-Type: application/json
```
**请求体：**
```json
{
  "progress_updates": [
    {
      "task_type": "catch_thieves",
      "increment": 1,
      "source_type": "thief",
      "source_id": "thief_001"
    }
  ]
}
```

## 6. 修改现有热点交互接口

### 热点交互（更新后）
```
POST /api/v1/game/hotspot/interact
Content-Type: application/json
```
**请求体：**
```json
{
  "hotspot_id": "thief_001",
  "hotspot_type": "thief",
  "action": "capture",
  "city_id": "beijing"
}
```
**响应示例（小偷交互）：**
```json
{
  "success": true,
  "data": {
    "hotspot_id": "thief_001",
    "hotspot_type": "thief",
    "rewards": {
      "civilization_exp": 15,
      "stamina_consumed": 2,
      "boss_damage": 5,
      "collection_drop": {
        "id": 3,
        "name": "炸酱面",
        "rarity": "common",
        "exp_bonus": 10
      },
      "treasure_box": {
        "box_type": "common",
        "requires_ad": true,
        "potential_items": ["magnifier", "radar", "stamina_potion"]
      }
    },
    "boss_status": {
      "current_hp": 950,
      "max_hp": 1000,
      "hp_percentage": 95.0,
      "trigger_dialogue": false,
      "next_dialogue_stage": "80"
    },
    "task_progress": [
      {
        "task_type": "catch_thieves",
        "progress_increment": 1,
        "new_progress": 4,
        "target": 10
      }
    ],
    "user_stats": {
      "civilization_exp": 1265,
      "guardian_level": 3,
      "stamina": 83,
      "collections_count": 9
    }
  }
}
```

## 7. 排行榜系统接口

### 获取文明经验排行榜
```
GET /api/v1/ranking/civilization-exp
Query参数: limit=50, city_id=beijing
```

### 获取图鉴收集排行榜
```
GET /api/v1/ranking/collections
Query参数: limit=50, city_id=beijing
```

### 获取综合排行榜
```
GET /api/v1/ranking/combined
Query参数: limit=50, city_id=beijing
```
**响应示例：**
```json
{
  "success": true,
  "data": {
    "ranking_type": "civilization_exp",
    "city_id": "beijing",
    "update_time": "2025-01-15T12:00:00Z",
    "rankings": [
      {
        "rank": 1,
        "user_id": 456,
        "nickname": "文明守护神",
        "avatar": "/avatars/user_456.jpg",
        "civilization_exp": 15000,
        "guardian_level": 8,
        "guardian_level_name": "传奇守卫",
        "collections_count": 45,
        "guardian_avatar_position": {
          "x": 120,
          "y": 180,
          "city_id": "beijing"
        }
      }
    ],
    "my_rank": {
      "rank": 25,
      "civilization_exp": 2500,
      "guardian_level": 5,
      "collections_count": 15
    }
  }
}
```

## 8. 体力系统接口

### 获取体力状态
```
GET /api/v1/user/stamina/status
```

### 消耗体力
```
POST /api/v1/user/stamina/consume
Content-Type: application/json
```
**请求体：**
```json
{
  "amount": 2,
  "reason": "capture_thief",
  "source_id": "thief_001"
}
```

### 恢复体力（观看广告）
```
POST /api/v1/user/stamina/recover
Content-Type: application/json
```
**请求体：**
```json
{
  "recovery_type": "ad_reward",
  "ad_completion_token": "ad_token_12345",
  "amount": 20
}
```