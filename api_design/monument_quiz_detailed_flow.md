# 古迹问答系统详细流程设计

## 🏛️ 古迹问答界面流程

根据游戏设计文档的描述，古迹问答系统需要实现以下完整流程：

### 1. 点击古迹热点 → 进入问答页面

#### API调用流程：
```
用户点击古迹热点 
   ↓
GET /api/v1/monuments/{monument_id}/info
   ↓
POST /api/v1/monuments/{monument_id}/start-challenge
```

#### 页面布局结构：
```
┌─────────────────────────────────────┐
│           古迹问答挑战页面              │
├─────────────────────────────────────┤
│  上部：被污染的古迹图片                 │
│  📸 [被垃圾占领的卢浮宫内部照片]        │
├─────────────────────────────────────┤
│  中部：垃圾头目对话区域                 │
│  👹 垃圾头目："失败者，这里已经被我们    │
│      占领了，如果你想夺回，问你几个      │
│      题目，回答对了，我们就撤退！"       │
├─────────────────────────────────────┤
│  问答题目区域                         │
│  ❓ 题目1：关于该古迹的历史问题          │
│     A) 选项1   B) 选项2               │
│     C) 选项3   D) 选项4               │
├─────────────────────────────────────┤
│  底部：挑战按钮                       │
│  [开始答题] [返回游戏]                 │
└─────────────────────────────────────┘
```

### 2. 开始答题挑战

#### API接口详细设计：

**开始挑战接口：**
```http
POST /api/v1/monuments/{monument_id}/start-challenge
Content-Type: application/json

请求体：
{
  "user_id": 123,
  "city_id": "beijing"
}

响应：
{
  "success": true,
  "data": {
    "challenge_id": "challenge_uuid_12345",
    "monument": {
      "id": 1,
      "name": "故宫太和殿",
      "polluted_image": "/monuments/beijing/taihebulding_polluted.jpg",
      "clean_image": "/monuments/beijing/taihebulding_clean.jpg",
      "boss_dialogue": {
        "intro": "失败者，这里已经被我们占领了，如果你想夺回，问你几个题目，回答对了，我们就撤退！",
        "avatar": "/bosses/monuments/trash_boss_avatar.png"
      }
    },
    "questions": [
      {
        "id": 1,
        "question_text": "太和殿主要用于什么重大场合？",
        "options": [
          {"id": 1, "text": "皇帝登基大典", "order": 1},
          {"id": 2, "text": "日常办公", "order": 2},
          {"id": 3, "text": "存放宝物", "order": 3},
          {"id": 4, "text": "皇后居住", "order": 4}
        ]
      },
      {
        "id": 2,
        "question_text": "太和殿屋顶使用什么颜色的琉璃瓦？",
        "options": [
          {"id": 5, "text": "金黄色", "order": 1},
          {"id": 6, "text": "深蓝色", "order": 2},
          {"id": 7, "text": "翠绿色", "order": 3},
          {"id": 8, "text": "紫色", "order": 4}
        ]
      },
      {
        "id": 3,
        "question_text": "紫禁城建于哪个朝代？",
        "options": [
          {"id": 9, "text": "明朝", "order": 1},
          {"id": 10, "text": "唐朝", "order": 2},
          {"id": 11, "text": "宋朝", "order": 3},
          {"id": 12, "text": "清朝", "order": 4}
        ]
      }
    ],
    "challenge_rules": {
      "total_questions": 3,
      "required_correct": 2,
      "success_exp_reward": 50,
      "failure_exp_penalty": -10,
      "boss_damage_on_success": 20,
      "time_limit_seconds": 300,
      "can_watch_ad_for_double": true
    }
  }
}
```

### 3. 提交答案并处理结果

#### 成功场景（答对2题以上）

**提交答案接口：**
```http
POST /api/v1/monuments/challenge/{challenge_id}/submit
Content-Type: application/json

请求体：
{
  "answers": [
    {"question_id": 1, "selected_option_id": 1},
    {"question_id": 2, "selected_option_id": 5}, 
    {"question_id": 3, "selected_option_id": 9}
  ],
  "time_spent_seconds": 245
}

成功响应：
{
  "success": true,
  "data": {
    "challenge_result": {
      "is_success": true,
      "questions_total": 3,
      "questions_correct": 3,
      "accuracy_rate": 100.0,
      "time_spent_seconds": 245
    },
    "rewards": {
      "civilization_exp": 50,
      "boss_damage_dealt": 20,
      "can_double_reward": true
    },
    "boss_dialogue": {
      "defeat_message": "算你厉害，我们先撤，但我还会回来的！",
      "avatar": "/bosses/monuments/trash_boss_defeated.png"
    },
    "monument_status": {
      "is_protected": true,
      "polluted_image": "/monuments/beijing/taihebulding_polluted.jpg",
      "clean_image": "/monuments/beijing/taihebulding_clean.jpg",
      "restoration_animation": "/monuments/animations/cleanup_effect.gif"
    },
    "answer_explanations": [
      {
        "question_id": 1,
        "is_correct": true,
        "explanation": "太和殿是皇帝举行登基、大婚、册封等重大典礼的场所"
      },
      {
        "question_id": 2, 
        "is_correct": true,
        "explanation": "太和殿采用金黄色琉璃瓦，象征皇权至上"
      },
      {
        "question_id": 3,
        "is_correct": true,
        "explanation": "紫禁城建于明朝永乐年间，历时14年建成"
      }
    ]
  }
}
```

#### 失败场景（答对题目不足）

```json
{
  "success": true,
  "data": {
    "challenge_result": {
      "is_success": false,
      "questions_total": 3,
      "questions_correct": 1,
      "accuracy_rate": 33.3,
      "time_spent_seconds": 280
    },
    "penalties": {
      "civilization_exp_lost": -10,
      "boss_damage_dealt": 0
    },
    "boss_dialogue": {
      "victory_message": "哈哈哈！有你守卫城市真是太好了，我们可以多待一会儿了，你赶紧回去吧！",
      "avatar": "/bosses/monuments/trash_boss_laughing.png"
    },
    "monument_status": {
      "is_protected": false,
      "remains_polluted": true
    },
    "answer_explanations": [
      {
        "question_id": 1,
        "is_correct": true,
        "explanation": "太和殿是皇帝举行登基、大婚、册封等重大典礼的场所"
      },
      {
        "question_id": 2,
        "is_correct": false,
        "user_answer": "深蓝色",
        "correct_answer": "金黄色", 
        "explanation": "太和殿采用金黄色琉璃瓦，象征皇权至上"
      }
    ]
  }
}
```

### 4. 双倍奖励机制

#### 观看广告获得双倍经验：

```http
POST /api/v1/monuments/challenge/{challenge_id}/double-reward
Content-Type: application/json

请求体：
{
  "ad_completion_token": "ad_token_uuid_67890",
  "ad_provider": "unity_ads",
  "ad_type": "rewarded_video"
}

响应：
{
  "success": true,
  "data": {
    "original_exp": 50,
    "bonus_exp": 50,
    "total_exp_gained": 100,
    "message": "观看广告成功！获得双倍文明经验奖励！"
  }
}
```

### 5. 前端页面状态流转

#### 成功流程界面变化：
```
1. 初始状态：显示被污染的古迹图片 + BOSS挑衅对话
   ↓
2. 答题阶段：显示问答题目，用户选择答案
   ↓ 
3. 提交答案：显示loading状态
   ↓
4. 成功结果：
   - 播放清理动画效果
   - 显示BOSS败退对话
   - 图片从污染版本切换到清洁版本
   - 显示经验奖励 +50
   - 提示观看广告获得双倍奖励
   ↓
5. 广告奖励（可选）：
   - 播放广告
   - 显示双倍奖励 +50 (总计+100)
```

#### 失败流程界面变化：
```
1-3. 同成功流程前三步
   ↓
4. 失败结果：
   - 显示BOSS胜利对话和嘲笑表情  
   - 古迹图片保持污染状态
   - 显示经验惩罚 -10
   - 显示答案解析
   - 提供"再次挑战"按钮
```

### 6. 数据库记录

每次挑战都会记录到 `user_monument_challenges` 表：
- 用户答题详情
- 获得/失去的经验值
- 对BOSS造成的伤害
- 答题耗时和准确率
- 是否观看广告获得双倍奖励

这个设计完全符合游戏文档的描述，提供了完整的古迹问答体验流程。