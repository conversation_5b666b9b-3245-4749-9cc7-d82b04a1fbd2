"""
API依赖注入
"""
from typing import Generator, Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from app.core.database import get_db
from app.core.redis_client import get_redis, RedisManager, redis_manager
from app.core.config import settings
from app.services.auth_service import auth_service
from app.models.user import User

logger = logging.getLogger(__name__)

# Bearer token认证
security = HTTPBearer()


async def get_current_user(
    db: AsyncSession = Depends(get_db),
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """获取当前用户"""
    token = credentials.credentials
    
    # 验证token
    user = await auth_service.get_current_user(db, token)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """获取当前活跃用户"""
    if not current_user.last_login_time:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


async def get_optional_user(
    db: AsyncSession = Depends(get_db),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[User]:
    """获取可选用户（用于支持游客和登录用户）"""
    if not credentials:
        return None
    
    token = credentials.credentials
    return await auth_service.get_current_user(db, token)


async def get_current_user_id(
    current_user: User = Depends(get_current_user)
) -> int:
    """获取当前用户ID"""
    return current_user.id


class RateLimitDep:
    """速率限制依赖"""
    
    def __init__(self, key_prefix: str, max_requests: int = 1000, window: int = 60):
        self.key_prefix = key_prefix
        self.max_requests = max_requests
        self.window = window
    
    async def __call__(
        self,
        user: User = Depends(get_current_user),
        redis: RedisManager = Depends(get_redis)
    ):
        """检查速率限制"""
        key = f"{self.key_prefix}:{user.user_id}"
        
        # 检查是否超过限制
        allowed = await redis.rate_limit(key, self.max_requests, self.window)
        if not allowed:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"Rate limit exceeded. Max {self.max_requests} requests per {self.window} seconds"
            )
        
        return True


# 常用的速率限制
rate_limit_session_start = RateLimitDep("session_start", max_requests=1000, window=3600)
rate_limit_ad_watch = RateLimitDep("ad_watch", max_requests=200, window=3600)
rate_limit_boss_attack = RateLimitDep("boss_attack", max_requests=1000, window=60) 