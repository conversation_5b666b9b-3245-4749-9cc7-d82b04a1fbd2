"""
管理员监控API - 查看系统性能指标
"""
import logging
from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any
from datetime import datetime, timedelta

from app.core.redis_client import redis_manager
from app.api.deps import get_current_user
from app.models.user import User

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/collection-cache/stats")
async def get_collection_cache_stats(
    current_user: User = Depends(get_current_user)
):
    """获取收集缓存统计信息"""
    try:
        # 检查用户权限（这里简化处理，实际应该检查管理员权限）
        
        from app.services.collection_cache_service import collection_cache_service
        
        # 获取队列状态
        queue_size = collection_cache_service.collection_queue.qsize()
        batch_size = collection_cache_service.batch_size
        batch_timeout = collection_cache_service.batch_timeout
        is_processing = collection_cache_service.is_processing
        
        # 获取Redis缓存信息
        redis_info = await get_redis_cache_info()
        
        return {
            "success": True,
            "data": {
                "queue_stats": {
                    "current_size": queue_size,
                    "max_size": collection_cache_service.collection_queue._maxsize,
                    "utilization": f"{(queue_size / collection_cache_service.collection_queue._maxsize) * 100:.1f}%",
                    "is_processing": is_processing
                },
                "batch_config": {
                    "batch_size": batch_size,
                    "batch_timeout_seconds": batch_timeout
                },
                "redis_cache": redis_info,
                "recommendations": get_performance_recommendations(queue_size, redis_info)
            }
        }
        
    except Exception as e:
        logger.error(f"获取缓存统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/collection-cache/active-sessions")
async def get_active_sessions(
    current_user: User = Depends(get_current_user)
):
    """获取活跃会话的缓存信息"""
    try:
        # 扫描所有会话缓存键
        session_keys = await redis_manager.keys("session:*:collections")
        
        active_sessions = []
        for key in session_keys:
            session_id = key.split(":")[1]
            cached_data = await redis_manager.hgetall(key)
            
            if cached_data:
                thieves = int(cached_data.get("thieves_collected", 0))
                garbage = int(cached_data.get("garbage_collected", 0))
                
                active_sessions.append({
                    "session_id": session_id,
                    "thieves_collected": thieves,
                    "garbage_collected": garbage,
                    "total_collected": thieves + garbage,
                    "cache_key": key
                })
        
        # 按收集总数排序
        active_sessions.sort(key=lambda x: x["total_collected"], reverse=True)
        
        return {
            "success": True,
            "data": {
                "total_active_sessions": len(active_sessions),
                "sessions": active_sessions[:50]  # 返回前50个最活跃的会话
            }
        }
        
    except Exception as e:
        logger.error(f"获取活跃会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取活跃会话失败: {str(e)}")


@router.get("/collection-cache/hotspot-status")
async def get_hotspot_status(
    session_id: str = None,
    current_user: User = Depends(get_current_user)
):
    """获取热点收集状态"""
    try:
        if session_id:
            # 获取特定会话的热点状态
            pattern = f"hotspot:{session_id}:*"
        else:
            # 获取所有热点状态
            pattern = "hotspot:*"
        
        hotspot_keys = await redis_manager.keys(pattern)
        
        collected_hotspots = []
        for key in hotspot_keys:
            hotspot_data = await redis_manager.hgetall(key)
            if hotspot_data and hotspot_data.get("collected"):
                parts = key.split(":")
                if len(parts) >= 3:
                    collected_hotspots.append({
                        "session_id": parts[1],
                        "hotspot_id": parts[2],
                        "collected_at": hotspot_data.get("collected_at"),
                        "user_id": hotspot_data.get("user_id")
                    })
        
        # 按收集时间排序
        collected_hotspots.sort(
            key=lambda x: x["collected_at"] or "", 
            reverse=True
        )
        
        return {
            "success": True,
            "data": {
                "total_collected_hotspots": len(collected_hotspots),
                "recent_collections": collected_hotspots[:100]  # 返回最近100个收集
            }
        }
        
    except Exception as e:
        logger.error(f"获取热点状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取热点状态失败: {str(e)}")


@router.post("/collection-cache/flush")
async def flush_collection_cache(
    current_user: User = Depends(get_current_user)
):
    """清空收集缓存（管理员操作）"""
    try:
        # 清空所有相关缓存
        session_keys = await redis_manager.keys("session:*:collections")
        hotspot_keys = await redis_manager.keys("hotspot:*")
        
        deleted_count = 0
        for key in session_keys + hotspot_keys:
            await redis_manager.delete(key)
            deleted_count += 1
        
        logger.warning(f"管理员 {current_user.id} 清空了收集缓存，删除了 {deleted_count} 个键")
        
        return {
            "success": True,
            "data": {
                "deleted_keys": deleted_count,
                "message": "收集缓存已清空"
            }
        }
        
    except Exception as e:
        logger.error(f"清空缓存失败: {e}")
        raise HTTPException(status_code=500, detail=f"清空缓存失败: {str(e)}")


async def get_redis_cache_info() -> Dict[str, Any]:
    """获取Redis缓存信息"""
    try:
        # 获取缓存键数量
        session_keys = await redis_manager.keys("session:*:collections")
        hotspot_keys = await redis_manager.keys("hotspot:*")
        
        # 计算缓存大小（估算）
        total_memory_usage = 0
        sample_size = min(10, len(session_keys))
        
        for key in session_keys[:sample_size]:
            # 获取键的内存使用量（Redis MEMORY USAGE命令）
            try:
                # 这里简化处理，实际可以使用Redis的MEMORY USAGE命令
                data = await redis_manager.hgetall(key)
                total_memory_usage += len(str(data)) * 2  # 粗略估算
            except:
                pass
        
        avg_session_size = total_memory_usage / max(sample_size, 1)
        estimated_total_size = avg_session_size * len(session_keys)
        
        return {
            "session_cache_keys": len(session_keys),
            "hotspot_cache_keys": len(hotspot_keys),
            "total_cache_keys": len(session_keys) + len(hotspot_keys),
            "estimated_memory_usage_bytes": int(estimated_total_size),
            "estimated_memory_usage_mb": round(estimated_total_size / 1024 / 1024, 2)
        }
        
    except Exception as e:
        logger.error(f"获取Redis信息失败: {e}")
        return {
            "session_cache_keys": 0,
            "hotspot_cache_keys": 0,
            "total_cache_keys": 0,
            "estimated_memory_usage_bytes": 0,
            "estimated_memory_usage_mb": 0,
            "error": str(e)
        }


def get_performance_recommendations(queue_size: int, redis_info: Dict[str, Any]) -> list:
    """生成性能优化建议"""
    recommendations = []
    
    # 队列使用率检查
    if queue_size > 8000:  # 80%使用率
        recommendations.append({
            "type": "warning",
            "message": "收集队列使用率过高，考虑增加批处理频率或批大小"
        })
    elif queue_size > 5000:  # 50%使用率
        recommendations.append({
            "type": "info",
            "message": "收集队列使用率中等，系统运行正常"
        })
    
    # 缓存键数量检查
    total_keys = redis_info.get("total_cache_keys", 0)
    if total_keys > 100000:
        recommendations.append({
            "type": "warning",
            "message": "缓存键数量过多，考虑调整TTL或清理策略"
        })
    
    # 内存使用检查
    memory_mb = redis_info.get("estimated_memory_usage_mb", 0)
    if memory_mb > 100:
        recommendations.append({
            "type": "warning",
            "message": f"缓存内存使用较高({memory_mb}MB)，建议监控Redis内存使用情况"
        })
    
    if not recommendations:
        recommendations.append({
            "type": "success",
            "message": "系统运行良好，缓存性能正常"
        })
    
    return recommendations 