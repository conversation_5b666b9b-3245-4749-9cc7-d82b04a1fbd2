"""
文化图鉴系统API端点
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional

from app.core.database import get_db
from app.models.user import User
from app.services.collection_service import CulturalCollectionService
from app.schemas.collections import (
    CityCollectionsResponse,
    CollectItemRequest,
    CollectItemResponse,
    UserCollectionStatsResponse
)
from app.api.deps import get_current_user

router = APIRouter()
collection_service = CulturalCollectionService()


@router.get("/city/{city_id}", response_model=CityCollectionsResponse)
async def get_city_collections(
    city_id: str,
    category: str = "all",
    rarity: str = "all", 
    collected: str = "all",
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取城市文化图鉴列表"""
    try:
        collections_data = await collection_service.get_city_collections(
            db=db,
            city_id=city_id,
            user_id=current_user.id,
            category_filter=category,
            rarity_filter=rarity,
            collected_filter=collected
        )
        return CityCollectionsResponse(success=True, data=collections_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取图鉴列表失败: {str(e)}"
        )


@router.post("/collect", response_model=CollectItemResponse)
async def collect_cultural_item(
    collect_data: CollectItemRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """收集文化图鉴"""
    try:
        result = await collection_service.collect_item(
            db=db,
            user_id=current_user.id,
            collection_id=collect_data.collection_id,
            collected_from=collect_data.collected_from,
            source_location=collect_data.source_location
        )
        return CollectItemResponse(success=True, data=result)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"收集图鉴失败: {str(e)}"
        )


@router.get("/user/stats", response_model=UserCollectionStatsResponse)
async def get_user_collection_stats(
    city_id: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户图鉴收集统计"""
    try:
        stats_data = await collection_service.get_user_stats(
            db=db,
            user_id=current_user.id,
            city_id=city_id
        )
        return UserCollectionStatsResponse(success=True, data=stats_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取收集统计失败: {str(e)}"
        )


@router.get("/random-drop/{city_id}")
async def get_random_collection_drop(
    city_id: str,
    source_type: str = "thief",  # thief, garbage, treasure_box
    rarity_boost: float = 1.0,  # 稀有度加成倍数
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取随机图鉴掉落（用于热点交互）"""
    try:
        drop_result = await collection_service.generate_random_drop(
            db=db,
            city_id=city_id,
            source_type=source_type,
            rarity_boost=rarity_boost,
            user_id=current_user.id
        )
        return {"success": True, "data": drop_result}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成随机掉落失败: {str(e)}"
        )