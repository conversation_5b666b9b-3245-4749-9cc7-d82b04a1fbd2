"""
文化问答系统API
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, Optional, List
from pydantic import BaseModel

from app.services.cultural_quiz_service import cultural_quiz_service
from app.api.deps import get_current_user_id

router = APIRouter()


# ==================== 请求模型 ====================

class QuizAnswerRequest(BaseModel):
    quiz_id: str
    selected_answers: List[str]
    time_spent: Optional[int] = None
    context: Optional[Dict[str, Any]] = None

class ArtifactCollectRequest(BaseModel):
    artifact_id: str
    source: str = "quiz_reward"
    context: Optional[Dict[str, Any]] = None

class MonumentRestoreRequest(BaseModel):
    monument_id: str
    artifact_ids: List[str]
    context: Optional[Dict[str, Any]] = None


# ==================== 文化问答API ====================

@router.get("/quiz/random")
async def get_random_quiz(
    difficulty: Optional[str] = None,
    category: Optional[str] = None,
    exclude_answered: bool = True,
    user_id: int = Depends(get_current_user_id)
):
    """获取随机问答题目"""
    result = await cultural_quiz_service.get_random_quiz(
        user_id, difficulty, category, exclude_answered
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获取题目失败"))
    return {"code": 200, "data": result, "message": "获取成功"}

@router.get("/quiz/{quiz_id}")
async def get_quiz_by_id(
    quiz_id: str,
    user_id: int = Depends(get_current_user_id)
):
    """根据ID获取问答题目"""
    result = await cultural_quiz_service.get_quiz_by_id(user_id, quiz_id)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获取题目失败"))
    return {"code": 200, "data": result, "message": "获取成功"}

@router.post("/quiz/answer")
async def answer_quiz(
    request: QuizAnswerRequest,
    user_id: int = Depends(get_current_user_id)
):
    """提交问答答案"""
    result = await cultural_quiz_service.answer_quiz(
        user_id, request.quiz_id, request.selected_answers, 
        request.time_spent, request.context
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "提交答案失败"))
    return {"code": 200, "data": result, "message": "提交成功"}

@router.get("/quiz/history")
async def get_quiz_history(
    limit: int = 20,
    offset: int = 0,
    user_id: int = Depends(get_current_user_id)
):
    """获取问答历史记录"""
    result = await cultural_quiz_service.get_quiz_history(user_id, limit, offset)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获取历史失败"))
    return {"code": 200, "data": result, "message": "获取成功"}

@router.get("/quiz/statistics")
async def get_quiz_statistics(
    days: int = 7,
    user_id: int = Depends(get_current_user_id)
):
    """获取问答统计信息"""
    result = await cultural_quiz_service.get_quiz_statistics(user_id, days)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获取统计失败"))
    return {"code": 200, "data": result, "message": "获取成功"}


# ==================== 文化图鉴API ====================

@router.get("/artifacts")
async def get_user_artifacts(
    category: Optional[str] = None,
    rarity: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    user_id: int = Depends(get_current_user_id)
):
    """获取用户文化图鉴"""
    result = await cultural_quiz_service.get_user_artifacts(
        user_id, category, rarity, limit, offset
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获取图鉴失败"))
    return {"code": 200, "data": result, "message": "获取成功"}

@router.get("/artifacts/{artifact_id}")
async def get_artifact_detail(
    artifact_id: str,
    user_id: int = Depends(get_current_user_id)
):
    """获取文化图鉴详情"""
    result = await cultural_quiz_service.get_artifact_detail(user_id, artifact_id)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获取详情失败"))
    return {"code": 200, "data": result, "message": "获取成功"}

@router.post("/artifacts/collect")
async def collect_artifact(
    request: ArtifactCollectRequest,
    user_id: int = Depends(get_current_user_id)
):
    """收集文化图鉴"""
    result = await cultural_quiz_service.collect_artifact(
        user_id, request.artifact_id, request.source, request.context
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "收集失败"))
    return {"code": 200, "data": result, "message": "收集成功"}

@router.get("/artifacts/collection_progress")
async def get_collection_progress(
    user_id: int = Depends(get_current_user_id)
):
    """获取收集进度"""
    result = await cultural_quiz_service.get_collection_progress(user_id)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获取进度失败"))
    return {"code": 200, "data": result, "message": "获取成功"}


# ==================== 古迹修复API ====================

@router.get("/monuments")
async def get_monuments(
    city_id: Optional[str] = None,
    status: Optional[str] = None,
    user_id: int = Depends(get_current_user_id)
):
    """获取古迹列表"""
    result = await cultural_quiz_service.get_monuments(user_id, city_id, status)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获取古迹失败"))
    return {"code": 200, "data": result, "message": "获取成功"}

@router.get("/monuments/{monument_id}")
async def get_monument_detail(
    monument_id: str,
    user_id: int = Depends(get_current_user_id)
):
    """获取古迹详情"""
    result = await cultural_quiz_service.get_monument_detail(user_id, monument_id)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获取详情失败"))
    return {"code": 200, "data": result, "message": "获取成功"}

@router.post("/monuments/restore")
async def restore_monument(
    request: MonumentRestoreRequest,
    user_id: int = Depends(get_current_user_id)
):
    """修复古迹"""
    result = await cultural_quiz_service.restore_monument(
        user_id, request.monument_id, request.artifact_ids, request.context
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "修复失败"))
    return {"code": 200, "data": result, "message": "修复成功"}

@router.get("/monuments/restoration_progress")
async def get_restoration_progress(
    user_id: int = Depends(get_current_user_id)
):
    """获取修复进度"""
    result = await cultural_quiz_service.get_restoration_progress(user_id)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获取进度失败"))
    return {"code": 200, "data": result, "message": "获取成功"}


# ==================== 文化教育内容API ====================

@router.get("/categories")
async def get_cultural_categories():
    """获取文化分类"""
    categories = {
        "world_heritage": {"name": "世界遗产", "description": "联合国教科文组织认定遗产"},
        "national_heritage": {"name": "国家文物", "description": "各国重要文化古迹建筑"},
        "local_culture": {"name": "地方特色", "description": "地方特色文化景点"},
        "intangible_heritage": {"name": "非遗技艺", "description": "传统手工艺、技能"},
        "folk_culture": {"name": "民俗文化", "description": "节庆、习俗、传说"},
        "historical_figures": {"name": "历史人物", "description": "重要历史文化人物"},
        "cuisine_culture": {"name": "特色美食", "description": "地方特色食物文化"}
    }
    return {"code": 200, "data": categories, "message": "获取成功"}

@router.get("/difficulty_levels")
async def get_difficulty_levels():
    """获取难度等级"""
    levels = {
        "easy": {"name": "简单", "description": "适合初学者", "exp_multiplier": 1.0},
        "medium": {"name": "中等", "description": "具有一定挑战性", "exp_multiplier": 1.2},
        "hard": {"name": "困难", "description": "需要深厚文化底蕴", "exp_multiplier": 1.5}
    }
    return {"code": 200, "data": levels, "message": "获取成功"}

@router.get("/achievements")
async def get_cultural_achievements(
    user_id: int = Depends(get_current_user_id)
):
    """获取文化成就"""
    result = await cultural_quiz_service.get_cultural_achievements(user_id)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获取成就失败"))
    return {"code": 200, "data": result, "message": "获取成功"}