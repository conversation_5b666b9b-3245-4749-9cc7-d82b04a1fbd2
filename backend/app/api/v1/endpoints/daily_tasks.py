"""
每日任务系统API  
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, Optional
from pydantic import BaseModel
from datetime import date

from app.services.daily_task_service import daily_task_service
from app.models.daily_task import TaskType
from app.api.deps import get_current_user_id

router = APIRouter()


# ==================== 请求模型 ====================

class TaskProgressUpdateRequest(BaseModel):
    task_type: TaskType
    progress_amount: int = 1
    context: Optional[Dict[str, Any]] = None

class TaskRewardClaimRequest(BaseModel):
    task_id: str
    use_ad_double: bool = False


# ==================== 任务管理API ====================

@router.get("/daily")
async def get_daily_tasks(
    task_date: Optional[str] = None,
    user_id: int = Depends(get_current_user_id)
):
    """获取用户每日任务"""
    try:
        if task_date:
            from datetime import datetime
            parsed_date = datetime.strptime(task_date, "%Y-%m-%d").date()
        else:
            parsed_date = None
            
        result = await daily_task_service.get_user_daily_tasks(user_id, parsed_date)
        if not result.get("success"):
            raise HTTPException(status_code=400, detail=result.get("error", "获取任务失败"))
        return {"code": 200, "data": result, "message": "获取成功"}
    except ValueError:
        raise HTTPException(status_code=400, detail="日期格式错误，应为YYYY-MM-DD")

@router.post("/daily/generate")
async def generate_daily_tasks(
    task_date: Optional[str] = None,
    user_id: int = Depends(get_current_user_id)
):
    """生成每日任务"""
    try:
        if task_date:
            from datetime import datetime
            parsed_date = datetime.strptime(task_date, "%Y-%m-%d").date()
        else:
            parsed_date = None
            
        result = await daily_task_service.generate_daily_tasks(user_id, parsed_date)
        if not result.get("success"):
            raise HTTPException(status_code=400, detail=result.get("error", "生成任务失败"))
        return {"code": 200, "data": result, "message": "生成成功"}
    except ValueError:
        raise HTTPException(status_code=400, detail="日期格式错误，应为YYYY-MM-DD")

@router.post("/progress/update")
async def update_task_progress(
    request: TaskProgressUpdateRequest,
    user_id: int = Depends(get_current_user_id)
):
    """更新任务进度"""
    result = await daily_task_service.update_task_progress(
        user_id, request.task_type, request.progress_amount, request.context
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "更新进度失败"))
    return {"code": 200, "data": result, "message": "更新成功"}

@router.post("/reward/claim")
async def claim_task_reward(
    request: TaskRewardClaimRequest,
    user_id: int = Depends(get_current_user_id)
):
    """领取任务奖励"""
    result = await daily_task_service.claim_task_reward(
        user_id, request.task_id, request.use_ad_double
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "领取奖励失败"))
    return {"code": 200, "data": result, "message": "领取成功"}

@router.get("/statistics")
async def get_task_statistics(
    days: int = 7,
    user_id: int = Depends(get_current_user_id)
):
    """获取任务统计信息"""
    result = await daily_task_service.get_task_statistics(user_id, days)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获取统计失败"))
    return {"code": 200, "data": result, "message": "获取成功"}


# ==================== 任务进度更新简化API ====================

@router.post("/progress/thief")
async def update_thief_task_progress(
    count: int = 1,
    context: Optional[Dict[str, Any]] = None,
    user_id: int = Depends(get_current_user_id)
):
    """更新抓捕小偷任务进度"""
    result = await daily_task_service.update_task_progress(
        user_id, TaskType.CATCH_THIEF, count, context
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "更新进度失败"))
    return {"code": 200, "data": result, "message": "进度更新成功"}

@router.post("/progress/rubbish")
async def update_rubbish_task_progress(
    count: int = 1,
    context: Optional[Dict[str, Any]] = None,
    user_id: int = Depends(get_current_user_id)
):
    """更新清理垃圾任务进度"""
    result = await daily_task_service.update_task_progress(
        user_id, TaskType.CLEAN_RUBBISH, count, context
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "更新进度失败"))
    return {"code": 200, "data": result, "message": "进度更新成功"}

@router.post("/progress/quiz")
async def update_quiz_task_progress(
    count: int = 1,
    context: Optional[Dict[str, Any]] = None,
    user_id: int = Depends(get_current_user_id)
):
    """更新文化学习任务进度"""
    result = await daily_task_service.update_task_progress(
        user_id, TaskType.CULTURAL_QUIZ, count, context
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "更新进度失败"))
    return {"code": 200, "data": result, "message": "进度更新成功"}

@router.post("/progress/perfect_clear")
async def update_perfect_clear_task_progress(
    count: int = 1,
    context: Optional[Dict[str, Any]] = None,
    user_id: int = Depends(get_current_user_id)
):
    """更新完美通关任务进度"""
    result = await daily_task_service.update_task_progress(
        user_id, TaskType.PERFECT_CLEAR, count, context
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "更新进度失败"))
    return {"code": 200, "data": result, "message": "进度更新成功"}