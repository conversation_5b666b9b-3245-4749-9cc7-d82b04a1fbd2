"""
经验值相关API端点
处理游戏中所有经验值获取操作
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.models.user import User
from app.api.deps import get_current_user
from app.services.experience_cache_service import experience_cache_service

router = APIRouter()


# ==================== 请求模型 ====================

class QuizCompletionRequest(BaseModel):
    """问答完成请求"""
    quiz_id: str = Field(..., description="问答ID")
    quiz_type: str = Field(..., description="问答类型：monument 或 cultural_collection")
    session_id: str = Field(..., description="游戏会话ID")
    question_count: int = Field(..., ge=1, description="题目总数")
    correct_count: int = Field(..., ge=0, description="答对题数")
    watch_ad_for_double: bool = Field(False, description="是否观看广告获得双倍奖励")


class TaskCompletionRequest(BaseModel):
    """任务完成请求"""
    task_id: str = Field(..., description="任务ID")
    task_type: str = Field(..., description="任务类型")
    watch_ad_for_double: bool = Field(False, description="是否观看广告获得双倍奖励")


class PassiveIncomeRequest(BaseModel):
    """被动收益请求"""
    duration_minutes: int = Field(..., ge=1, description="离线时长（分钟）")
    watch_ad_for_double: bool = Field(False, description="是否观看广告获得双倍奖励")


class StaminaUpdateRequest(BaseModel):
    """体力值更新请求"""
    stamina_change: int = Field(..., description="体力值变化（正数增加，负数消耗）")
    reason: str = Field("游戏操作", description="体力变化原因")


# ==================== 问答相关 ====================

@router.post("/quiz/complete")
async def complete_quiz(
    request: QuizCompletionRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """完成问答，获得经验值"""
    try:
        # 计算基础经验值
        if request.quiz_type == "monument":
            # 古迹问答：成功 50 经验，部分正确按比例
            base_experience = int(50 * (request.correct_count / request.question_count))
        elif request.quiz_type == "cultural_collection":
            # 文化图鉴问答：成功 15 经验，部分正确按比例
            base_experience = int(15 * (request.correct_count / request.question_count))
        else:
            base_experience = 10 * request.correct_count
        
        # 双倍奖励
        experience_gained = base_experience * 2 if request.watch_ad_for_double else base_experience
        
        # 缓存经验值
        result = await experience_cache_service.cache_quiz_experience(
            user_id=current_user.id,
            session_id=request.session_id,
            quiz_id=request.quiz_id,
            quiz_type=request.quiz_type,
            question_count=request.question_count,
            correct_count=request.correct_count,
            experience_gained=experience_gained,
            is_double_reward=request.watch_ad_for_double
        )
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "success": True,
            "message": "问答完成！" if request.correct_count == request.question_count else "继续努力！",
            "experience_gained": experience_gained,
            "total_experience": result["total_experience"],
            "accuracy": result["accuracy"],
            "is_perfect": request.correct_count == request.question_count,
            "cached": True
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"问答完成失败: {str(e)}")


@router.get("/quiz/stats/{user_id}")
async def get_quiz_stats(
    user_id: int,
    current_user: User = Depends(get_current_user)
):
    """获取用户问答统计"""
    try:
        # 只能查看自己的统计
        if user_id != current_user.id:
            raise HTTPException(status_code=403, detail="无权限查看")
        
        stats = await experience_cache_service.get_user_cache_stats(user_id)
        quiz_stats = stats.get("quiz_stats", {})
        
        return {
            "success": True,
            "data": {
                "monument_answered": quiz_stats.get("monument_answered", 0),
                "monument_correct": quiz_stats.get("monument_correct", 0),
                "cultural_collection_answered": quiz_stats.get("cultural_collection_answered", 0),
                "cultural_collection_correct": quiz_stats.get("cultural_collection_correct", 0),
                "total_quiz_experience": quiz_stats.get("total_quiz_experience", 0),
                "monument_accuracy": (
                    quiz_stats.get("monument_correct", 0) / quiz_stats.get("monument_answered", 1) 
                    if quiz_stats.get("monument_answered", 0) > 0 else 0
                ),
                "cultural_accuracy": (
                    quiz_stats.get("cultural_collection_correct", 0) / quiz_stats.get("cultural_collection_answered", 1)
                    if quiz_stats.get("cultural_collection_answered", 0) > 0 else 0
                )
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取问答统计失败: {str(e)}")


# ==================== 任务相关 ====================

@router.post("/task/complete")
async def complete_task(
    request: TaskCompletionRequest,
    current_user: User = Depends(get_current_user)
):
    """完成任务，获得经验值"""
    try:
        # 根据任务类型计算经验值
        task_experience_map = {
            "catch_thieves_10": 20,
            "catch_thieves_50": 100,
            "catch_thieves_100": 250,
            "clean_garbage_10": 15,
            "clean_garbage_50": 80,
            "clean_garbage_100": 200,
            "collect_artifacts_1": 30,
            "collect_artifacts_5": 150,
            "protect_monuments_1": 50,
            "protect_monuments_5": 300,
        }
        
        base_experience = task_experience_map.get(request.task_type, 20)
        experience_gained = base_experience * 2 if request.watch_ad_for_double else base_experience
        
        # 缓存任务完成
        result = await experience_cache_service.cache_task_experience(
            user_id=current_user.id,
            task_id=request.task_id,
            task_type=request.task_type,
            experience_gained=experience_gained,
            is_double_reward=request.watch_ad_for_double
        )
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "success": True,
            "message": "任务完成！获得守护经验奖励！",
            "task_type": request.task_type,
            "experience_gained": experience_gained,
            "total_experience": result["total_experience"],
            "is_double_reward": request.watch_ad_for_double,
            "cached": True
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"任务完成失败: {str(e)}")


# ==================== 守望者被动收益 ====================

@router.post("/passive-income/collect")
async def collect_passive_income(
    request: PassiveIncomeRequest,
    current_user: User = Depends(get_current_user)
):
    """收集守望者头像被动收益"""
    try:
        # 根据守护者等级计算被动收益率
        guardian_level = getattr(current_user, 'guardian_level', 1)
        income_rate_map = {
            1: 1, 2: 2, 3: 3, 4: 5, 5: 8,
            6: 12, 7: 18, 8: 25, 9: 35, 10: 50,
            11: 70, 12: 100, 13: 150, 14: 200
        }
        
        income_rate = income_rate_map.get(guardian_level, 1)
        
        # 缓存被动收益
        result = await experience_cache_service.cache_passive_income(
            user_id=current_user.id,
            guardian_level=guardian_level,
            income_rate=income_rate,
            duration_minutes=request.duration_minutes
        )
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        # 双倍奖励处理
        if request.watch_ad_for_double:
            additional_exp = result["experience_gained"]
            # 再次缓存双倍部分
            double_result = await experience_cache_service.cache_passive_income(
                user_id=current_user.id,
                guardian_level=guardian_level,
                income_rate=income_rate,
                duration_minutes=request.duration_minutes
            )
            result["experience_gained"] = result["experience_gained"] + additional_exp
            result["total_experience"] = double_result.get("total_experience", result["total_experience"])
        
        return {
            "success": True,
            "message": f"守望者头像为你带来了 {result['experience_gained']} 点守护经验！",
            "guardian_level": guardian_level,
            "income_rate": income_rate,
            "duration_minutes": request.duration_minutes,
            "experience_gained": result["experience_gained"],
            "total_experience": result["total_experience"],
            "is_double_reward": request.watch_ad_for_double,
            "cached": True
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"收集被动收益失败: {str(e)}")


# ==================== 体力值管理 ====================

@router.post("/stamina/update")
async def update_stamina(
    request: StaminaUpdateRequest,
    current_user: User = Depends(get_current_user)
):
    """更新用户体力值"""
    try:
        result = await experience_cache_service.update_stamina(
            user_id=current_user.id,
            stamina_change=request.stamina_change,
            reason=request.reason
        )
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "success": True,
            "current_stamina": result["current_stamina"],
            "stamina_change": result["stamina_change"],
            "is_low_stamina": result["is_low_stamina"],
            "message": "体力不足，获得的经验减半！" if result["is_low_stamina"] else "体力充足！"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新体力值失败: {str(e)}")


@router.get("/stamina/status")
async def get_stamina_status(
    current_user: User = Depends(get_current_user)
):
    """获取用户体力状态"""
    try:
        stats = await experience_cache_service.get_user_cache_stats(current_user.id)
        stamina = stats.get("stamina", 100)
        
        return {
            "success": True,
            "current_stamina": stamina,
            "max_stamina": 100,
            "stamina_percentage": stamina / 100,
            "is_low_stamina": stamina < 20,
            "is_full_stamina": stamina >= 100
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取体力状态失败: {str(e)}")


# ==================== 经验值同步 ====================

@router.post("/sync")
async def sync_experience_to_database(
    current_user: User = Depends(get_current_user)
):
    """同步缓存的经验值到数据库"""
    try:
        await experience_cache_service.sync_user_experience_to_database(current_user.id)
        
        return {
            "success": True,
            "message": "经验值同步完成"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"同步经验值失败: {str(e)}")


@router.get("/stats")
async def get_experience_stats(
    current_user: User = Depends(get_current_user)
):
    """获取用户完整的经验值统计"""
    try:
        stats = await experience_cache_service.get_user_cache_stats(current_user.id)
        
        return {
            "success": True,
            "data": {
                "total_experience": stats.get("experience", 0),
                "current_stamina": stats.get("stamina", 100),
                "quiz_stats": stats.get("quiz_stats", {}),
                "task_stats": stats.get("task_stats", {}),
                "guardian_level": getattr(current_user, 'guardian_level', 1),
                "cached_data": True
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取经验统计失败: {str(e)}") 