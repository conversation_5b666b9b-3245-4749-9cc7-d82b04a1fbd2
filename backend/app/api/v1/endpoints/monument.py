"""
古迹问答系统API端点
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
import uuid

from app.core.database import get_db
from app.models.user import User
from app.services.monument_service import MonumentService
from app.schemas.monument import (
    MonumentListResponse,
    MonumentChallengeStartRequest,
    MonumentChallengeStartResponse,
    MonumentChallengeSubmitRequest,
    MonumentChallengeSubmitResponse,
    MonumentDoubleRewardRequest,
    MonumentDoubleRewardResponse
)
from app.api.deps import get_current_user

router = APIRouter()
monument_service = MonumentService()


@router.get("/{city_id}", response_model=MonumentListResponse)
async def get_monuments(
    city_id: str,
    db: Session = Depends(get_db)
):
    """获取城市古迹列表"""
    try:
        monuments_data = await monument_service.get_city_monuments(db, city_id)
        return MonumentListResponse(success=True, data=monuments_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取古迹列表失败: {str(e)}"
        )


@router.post("/{monument_id}/start-challenge", response_model=MonumentChallengeStartResponse)
async def start_monument_challenge(
    monument_id: int,
    challenge_data: MonumentChallengeStartRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """开始古迹问答挑战"""
    try:
        # 生成唯一挑战ID
        challenge_id = f"challenge_{uuid.uuid4().hex[:12]}"
        
        result = await monument_service.start_challenge(
            db=db,
            user_id=current_user.id,
            monument_id=monument_id,
            challenge_id=challenge_id,
            city_id=challenge_data.city_id
        )
        return MonumentChallengeStartResponse(success=True, data=result)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"开始挑战失败: {str(e)}"
        )


@router.post("/challenge/{challenge_id}/submit", response_model=MonumentChallengeSubmitResponse)
async def submit_monument_challenge(
    challenge_id: str,
    submit_data: MonumentChallengeSubmitRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """提交古迹问答答案"""
    try:
        result = await monument_service.submit_challenge(
            db=db,
            user_id=current_user.id,
            challenge_id=challenge_id,
            answers=submit_data.answers,
            time_spent_seconds=submit_data.time_spent_seconds
        )
        return MonumentChallengeSubmitResponse(success=True, data=result)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"提交答案失败: {str(e)}"
        )


@router.post("/challenge/{challenge_id}/double-reward", response_model=MonumentDoubleRewardResponse)
async def get_double_reward(
    challenge_id: str,
    reward_data: MonumentDoubleRewardRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """观看广告获得双倍奖励"""
    try:
        result = await monument_service.claim_double_reward(
            db=db,
            user_id=current_user.id,
            challenge_id=challenge_id,
            ad_completion_token=reward_data.ad_completion_token,
            ad_provider=reward_data.ad_provider,
            ad_type=reward_data.ad_type
        )
        return MonumentDoubleRewardResponse(success=True, data=result)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"领取双倍奖励失败: {str(e)}"
        )