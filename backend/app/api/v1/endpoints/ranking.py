"""
新排行榜系统API端点 - 文明守护者系统
"""
from fastapi import APIRouter, Depends, Query, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

from app.core.database import get_db
from app.core.redis_client import get_redis, RedisManager
from app.models.user import User
from app.services.ranking_service import RankingService
from app.schemas.ranking import (
    CivilizationRankingResponse,
    CollectionsRankingResponse,
    CombinedRankingResponse,
    PlayerDetailResponse
)
from app.api.deps import get_current_user, get_optional_user
from app.analytics.metrics import track_api_performance

router = APIRouter()
ranking_service = RankingService()


@router.get("/civilization-exp", response_model=CivilizationRankingResponse)
@track_api_performance("/ranking/civilization-exp")
async def get_civilization_exp_ranking(
    limit: int = Query(50, ge=1, le=500, description="返回数量"),
    city_id: Optional[str] = Query(None, description="特定城市排行榜"),
    current_user: Optional[User] = Depends(get_optional_user),
    db: Session = Depends(get_db),
    redis: RedisManager = Depends(get_redis)
):
    """获取文明经验排行榜"""
    try:
        ranking_data = await ranking_service.get_civilization_exp_ranking(
            db=db,
            redis=redis,
            limit=limit,
            city_id=city_id,
            current_user_id=current_user.id if current_user else None
        )
        return CivilizationRankingResponse(success=True, data=ranking_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取文明经验排行榜失败: {str(e)}"
        )


@router.get("/collections", response_model=CollectionsRankingResponse)  
@track_api_performance("/ranking/collections")
async def get_collections_ranking(
    limit: int = Query(50, ge=1, le=500, description="返回数量"),
    city_id: Optional[str] = Query(None, description="特定城市排行榜"),
    current_user: Optional[User] = Depends(get_optional_user),
    db: Session = Depends(get_db),
    redis: RedisManager = Depends(get_redis)
):
    """获取图鉴收集排行榜"""
    try:
        ranking_data = await ranking_service.get_collections_ranking(
            db=db,
            redis=redis,
            limit=limit,
            city_id=city_id,
            current_user_id=current_user.id if current_user else None
        )
        return CollectionsRankingResponse(success=True, data=ranking_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取图鉴收集排行榜失败: {str(e)}"
        )


@router.get("/combined", response_model=CombinedRankingResponse)
@track_api_performance("/ranking/combined")
async def get_combined_ranking(
    limit: int = Query(50, ge=1, le=500, description="返回数量"),
    city_id: Optional[str] = Query(None, description="特定城市排行榜"),
    current_user: Optional[User] = Depends(get_optional_user),
    db: Session = Depends(get_db),
    redis: RedisManager = Depends(get_redis)
):
    """获取综合排行榜（文明经验 + 图鉴收集综合评分）"""
    try:
        ranking_data = await ranking_service.get_combined_ranking(
            db=db,
            redis=redis,
            limit=limit,
            city_id=city_id,
            current_user_id=current_user.id if current_user else None
        )
        return CombinedRankingResponse(success=True, data=ranking_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取综合排行榜失败: {str(e)}"
        )


@router.get("/player/{user_id}/guardian-details", response_model=PlayerDetailResponse)
@track_api_performance("/ranking/player-guardian-details") 
async def get_player_guardian_details(
    user_id: str,
    db: Session = Depends(get_db)
):
    """获取玩家文明守护者详细信息"""
    try:
        player_data = await ranking_service.get_player_guardian_details(db, user_id)
        return PlayerDetailResponse(success=True, data=player_data)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取玩家详情失败: {str(e)}"
        )


@router.post("/refresh/{ranking_type}")
async def refresh_ranking(
    ranking_type: str,
    city_id: Optional[str] = None,
    db: Session = Depends(get_db),
    redis: RedisManager = Depends(get_redis)
):
    """刷新排行榜数据（管理员功能）"""
    try:
        if ranking_type not in ["civilization_exp", "collections", "combined"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的排行榜类型"
            )
            
        await ranking_service.refresh_ranking_cache(
            db=db,
            redis=redis,
            ranking_type=ranking_type,
            city_id=city_id
        )
        return {"success": True, "message": f"{ranking_type} 排行榜已刷新"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"刷新排行榜失败: {str(e)}"
        )


@router.get("/my-rank")
async def get_my_rankings(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    redis: RedisManager = Depends(get_redis)
):
    """获取当前用户在各排行榜中的排名"""
    try:
        my_ranks = await ranking_service.get_user_all_rankings(
            db=db,
            redis=redis,
            user_id=current_user.id
        )
        return {"success": True, "data": my_ranks}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户排名失败: {str(e)}"
        )


@router.get("/stats/global")
async def get_global_ranking_stats(
    db: Session = Depends(get_db)
):
    """获取全球排行榜统计信息"""
    try:
        stats_data = await ranking_service.get_global_stats(db)
        return {"success": True, "data": stats_data}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取全球统计失败: {str(e)}"
        )


# 保持向后兼容的旧接口名称
@router.get("/artifact-collectors", response_model=CollectionsRankingResponse)
async def get_artifact_collectors_legacy(
    limit: int = Query(50, ge=1, le=500),
    current_user: Optional[User] = Depends(get_optional_user),
    db: Session = Depends(get_db),
    redis: RedisManager = Depends(get_redis)
):
    """旧版文物收藏者排行榜接口（向后兼容）"""
    return await get_collections_ranking(
        limit=limit,
        city_id=None,
        current_user=current_user,
        db=db,
        redis=redis
    )