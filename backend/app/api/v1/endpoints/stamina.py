"""
体力系统API端点
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from datetime import datetime

from app.core.database import get_db
from app.models.user import User
from app.services.stamina_service import StaminaService
from app.schemas.stamina import (
    StaminaStatusResponse,
    StaminaConsumeRequest,
    StaminaConsumeResponse,
    StaminaRecoverRequest,
    StaminaRecoverResponse
)
from app.api.deps import get_current_user

router = APIRouter()
stamina_service = StaminaService()


@router.get("/status", response_model=StaminaStatusResponse)
async def get_stamina_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户体力状态"""
    try:
        status_data = await stamina_service.get_stamina_status(db, current_user.id)
        return StaminaStatusResponse(success=True, data=status_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取体力状态失败: {str(e)}"
        )


@router.post("/consume", response_model=StaminaConsumeResponse)
async def consume_stamina(
    consume_data: StaminaConsumeRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """消耗体力"""
    try:
        result = await stamina_service.consume_stamina(
            db=db,
            user_id=current_user.id,
            amount=consume_data.amount,
            reason=consume_data.reason,
            source_id=consume_data.source_id
        )
        return StaminaConsumeResponse(success=True, data=result)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"消耗体力失败: {str(e)}"
        )


@router.post("/recover", response_model=StaminaRecoverResponse)
async def recover_stamina(
    recover_data: StaminaRecoverRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """恢复体力"""
    try:
        result = await stamina_service.recover_stamina(
            db=db,
            user_id=current_user.id,
            recovery_type=recover_data.recovery_type,
            ad_completion_token=recover_data.ad_completion_token,
            amount=recover_data.amount
        )
        return StaminaRecoverResponse(success=True, data=result)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"恢复体力失败: {str(e)}"
        )


@router.post("/natural-recovery")
async def trigger_natural_recovery(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """触发自然回复（每隔一段时间自动调用）"""
    try:
        result = await stamina_service.natural_recovery(db, current_user.id)
        return {"success": True, "data": result}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"自然回复失败: {str(e)}"
        )


@router.get("/recovery-info")
async def get_recovery_info(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取体力回复信息"""
    try:
        info_data = await stamina_service.get_recovery_info(db, current_user.id)
        return {"success": True, "data": info_data}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取回复信息失败: {str(e)}"
        )