"""
宝箱系统API
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, Optional
from pydantic import BaseModel

from app.services.treasure_box_service import treasure_box_service
from app.api.deps import get_current_user_id

router = APIRouter()


# ==================== 请求模型 ====================

class TreasureBoxOpenRequest(BaseModel):
    box_id: str
    source_action: str
    use_ad_double: bool = False
    context: Optional[Dict[str, Any]] = None

class TreasureBoxClaimRequest(BaseModel):
    box_id: str
    use_ad_double: bool = False


# ==================== 宝箱管理API ====================

@router.get("/user/boxes")
async def get_user_treasure_boxes(
    include_opened: bool = False,
    user_id: int = Depends(get_current_user_id)
):
    """获取用户宝箱列表"""
    result = await treasure_box_service.get_user_treasure_boxes(
        user_id, include_opened
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获取宝箱失败"))
    return {"code": 200, "data": result, "message": "获取成功"}

@router.post("/open")
async def open_treasure_box(
    request: TreasureBoxOpenRequest,
    user_id: int = Depends(get_current_user_id)
):
    """开启宝箱"""
    result = await treasure_box_service.open_treasure_box(
        user_id, request.box_id, request.source_action, 
        request.use_ad_double, request.context
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "开启宝箱失败"))
    return {"code": 200, "data": result, "message": "开启成功"}

@router.post("/claim")
async def claim_treasure_box_reward(
    request: TreasureBoxClaimRequest,
    user_id: int = Depends(get_current_user_id)
):
    """领取宝箱奖励"""
    result = await treasure_box_service.claim_treasure_box_reward(
        user_id, request.box_id, request.use_ad_double
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "领取奖励失败"))
    return {"code": 200, "data": result, "message": "领取成功"}

@router.get("/statistics")
async def get_treasure_box_statistics(
    days: int = 7,
    user_id: int = Depends(get_current_user_id)
):
    """获取宝箱统计信息"""
    result = await treasure_box_service.get_treasure_box_statistics(user_id, days)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获取统计失败"))
    return {"code": 200, "data": result, "message": "获取成功"}


# ==================== 宝箱触发API ====================

@router.post("/trigger/thief")
async def trigger_thief_treasure_box(
    count: int = 1,
    context: Optional[Dict[str, Any]] = None,
    user_id: int = Depends(get_current_user_id)
):
    """抓捕小偷触发宝箱"""
    result = await treasure_box_service.trigger_treasure_box(
        user_id, "catch_thief", count, context
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "触发宝箱失败"))
    return {"code": 200, "data": result, "message": "检查完成"}

@router.post("/trigger/rubbish")
async def trigger_rubbish_treasure_box(
    count: int = 1,
    context: Optional[Dict[str, Any]] = None,
    user_id: int = Depends(get_current_user_id)
):
    """清理垃圾触发宝箱"""
    result = await treasure_box_service.trigger_treasure_box(
        user_id, "clean_rubbish", count, context
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "触发宝箱失败"))
    return {"code": 200, "data": result, "message": "检查完成"}

@router.post("/trigger/monument")
async def trigger_monument_treasure_box(
    correct_answers: int = 1,
    context: Optional[Dict[str, Any]] = None,
    user_id: int = Depends(get_current_user_id)
):
    """古迹问答触发宝箱"""
    result = await treasure_box_service.trigger_treasure_box(
        user_id, "monument_quiz", correct_answers, context
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "触发宝箱失败"))
    return {"code": 200, "data": result, "message": "检查完成"}

@router.post("/trigger/boss_defeat")
async def trigger_boss_defeat_treasure_box(
    session_id: str,
    city_id: str,
    context: Optional[Dict[str, Any]] = None,
    user_id: int = Depends(get_current_user_id)
):
    """BOSS击败触发金宝箱"""
    result = await treasure_box_service.trigger_treasure_box(
        user_id, "boss_defeat", 1, {
            "session_id": session_id, 
            "city_id": city_id, 
            **(context or {})
        }
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "触发宝箱失败"))
    return {"code": 200, "data": result, "message": "金宝箱生成成功"}


# ==================== 宝箱配置API ====================

@router.get("/config")
async def get_treasure_box_config():
    """获取宝箱配置"""
    result = await treasure_box_service.get_treasure_box_config()
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获取配置失败"))
    return {"code": 200, "data": result, "message": "获取成功"}

@router.get("/drop_rates")
async def get_treasure_box_drop_rates():
    """获取宝箱掉落率信息"""
    drop_rates = {
        "catch_thief": {"rate": 6, "box_type": "bronze", "description": "抓捕小偷 - 铜宝箱"},
        "clean_rubbish": {"rate": 3, "box_type": "bronze", "description": "清理垃圾 - 铜宝箱"},
        "monument_quiz": {"rate": 15, "box_type": "silver", "description": "古迹问答 - 银宝箱"},
        "boss_defeat": {"rate": 100, "box_type": "gold", "description": "击败BOSS - 金宝箱（必掉）"}
    }
    return {"code": 200, "data": drop_rates, "message": "获取成功"}