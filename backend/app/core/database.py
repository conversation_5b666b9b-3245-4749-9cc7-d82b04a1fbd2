"""
数据库连接管理
使用 SQLAlchemy 2.0 + aiomysql 实现异步数据库操作
"""
from typing import AsyncGenerator, Optional
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy import MetaData, event, Pool
from sqlalchemy.pool import NullPool
import logging
from contextlib import asynccontextmanager

from app.core.config import settings

logger = logging.getLogger(__name__)

# 定义数据库元数据命名约定
NAMING_CONVENTION = {
    "ix": "ix_%(column_0_label)s",
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s"
}

metadata = MetaData(naming_convention=NAMING_CONVENTION)


class Base(DeclarativeBase):
    """数据库模型基类"""
    metadata = metadata


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = None
        self.sessionmaker = None
        self._initialized = False
    
    async def initialize(self):
        """初始化数据库连接"""
        if self._initialized:
            return
        
        # 获取优化配置
        from app.core.database_optimization import mysql_optimizer
        optimized_config = mysql_optimizer.get_optimized_engine_config()
        
        # 创建异步引擎（使用优化配置）
        self.engine = create_async_engine(
            settings.DATABASE_URL,
            echo=settings.DEBUG,
            **optimized_config
        )
        
        # 创建会话工厂
        self.sessionmaker = async_sessionmaker(
            self.engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autocommit=False,
            autoflush=False,
        )
        
        # 设置连接池监听器（用于监控）
        if settings.ENABLE_ANALYTICS:
            @event.listens_for(self.engine.sync_engine, "connect")
            def receive_connect(dbapi_conn, connection_record):
                logger.info("Database connection established")
            
            @event.listens_for(self.engine.sync_engine, "close")
            def receive_close(dbapi_conn, connection_record):
                logger.info("Database connection closed")
        
        self._initialized = True
        logger.info("Database initialized successfully")
    
    async def close(self):
        """关闭数据库连接"""
        if self.engine:
            await self.engine.dispose()
            self._initialized = False
            logger.info("Database connections closed")
    
    @asynccontextmanager
    async def session(self) -> AsyncGenerator[AsyncSession, None]:
        """获取数据库会话上下文管理器"""
        if not self._initialized:
            await self.initialize()
        
        async with self.sessionmaker() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def get_session(self) -> AsyncSession:
        """获取数据库会话（用于依赖注入）"""
        if not self._initialized:
            await self.initialize()
        
        async with self.sessionmaker() as session:
            yield session
    
    async def execute_raw(self, query: str, params: Optional[dict] = None):
        """执行原始SQL查询"""
        async with self.session() as session:
            result = await session.execute(query, params)
            return result
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            from sqlalchemy import text
            async with self.session() as session:
                await session.execute(text("SELECT 1"))
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    def get_pool_status(self) -> dict:
        """获取连接池状态（用于监控）"""
        if not self.engine:
            return {"status": "not_initialized"}
        
        pool = self.engine.pool
        return {
            "size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "total": pool.size() + pool.overflow()
        }


# 创建全局数据库管理器实例
db_manager = DatabaseManager()


# 依赖注入函数
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """FastAPI 依赖注入函数"""
    async for session in db_manager.get_session():
        yield session


# 数据库初始化脚本
async def init_db():
    """初始化数据库表结构"""
    await db_manager.initialize()
    
    # 导入所有模型（确保表被创建）
    from app.models import user, game, artifact, config  # noqa
    
    async with db_manager.engine.begin() as conn:
        # 创建所有表
        await conn.run_sync(Base.metadata.create_all)
    
    logger.info("Database tables created successfully")


# 事务装饰器
def transactional(func):
    """事务装饰器，自动管理事务"""
    async def wrapper(*args, **kwargs):
        async with db_manager.session() as session:
            # 将session注入到函数参数中
            return await func(*args, session=session, **kwargs)
    return wrapper 