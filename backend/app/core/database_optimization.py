"""
MySQL数据库优化配置
针对VR城市游戏的高并发和高频写入场景优化
"""
import logging
from typing import Dict, Any, Optional
from sqlalchemy import text, create_engine
from app.core.config import settings

logger = logging.getLogger(__name__)


class MySQLOptimizer:
    """MySQL数据库优化器"""
    
    @staticmethod
    def get_optimized_engine_config() -> Dict[str, Any]:
        """获取优化的引擎配置"""
        return {
            "pool_size": 50,  # 增加连接池大小
            "max_overflow": 100,  # 增加溢出连接
            "pool_pre_ping": True,  # 连接检测
            "pool_recycle": 3600,  # 1小时回收连接
            "pool_timeout": 30,  # 连接超时
            # echo参数在database.py中已设置，此处不重复设置
            # pool_class在异步引擎中自动选择，不需要手动指定
            "connect_args": {
                "charset": "utf8mb4",
                "autocommit": False,
                "connect_timeout": 10,
                # aiomysql不支持read_timeout和write_timeout参数
                # MySQL优化参数
                "init_command": """
                    SET SESSION sql_mode='STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';
                    SET SESSION innodb_lock_wait_timeout=5;
                    SET SESSION lock_wait_timeout=5;
                """.strip()
            }
        }
    
    @staticmethod
    def get_mysql_server_optimization_sql() -> str:
        """获取MySQL服务器优化SQL脚本"""
        return """
        -- InnoDB优化
        SET GLOBAL innodb_buffer_pool_size = 2147483648;  -- 2GB
        SET GLOBAL innodb_log_file_size = 268435456;      -- 256MB
        SET GLOBAL innodb_flush_log_at_trx_commit = 2;    -- 每秒刷新，平衡性能和安全
        SET GLOBAL innodb_flush_method = 'O_DIRECT';      -- 避免双重缓冲
        SET GLOBAL innodb_io_capacity = 2000;             -- 根据SSD调整
        SET GLOBAL innodb_read_io_threads = 8;            -- 读线程数
        SET GLOBAL innodb_write_io_threads = 8;           -- 写线程数
        
        -- 连接优化
        SET GLOBAL max_connections = 1000;               -- 最大连接数
        SET GLOBAL thread_cache_size = 50;               -- 线程缓存
        SET GLOBAL interactive_timeout = 300;            -- 交互超时
        SET GLOBAL wait_timeout = 300;                   -- 等待超时
        
        -- 查询缓存优化
        SET GLOBAL query_cache_type = 1;                 -- 开启查询缓存
        SET GLOBAL query_cache_size = 268435456;         -- 256MB查询缓存
        SET GLOBAL query_cache_limit = 2097152;          -- 2MB单查询缓存限制
        
        -- 临时表优化
        SET GLOBAL tmp_table_size = 268435456;           -- 256MB临时表
        SET GLOBAL max_heap_table_size = 268435456;      -- 256MB内存表
        
        -- 排序优化
        SET GLOBAL sort_buffer_size = 2097152;           -- 2MB排序缓冲
        SET GLOBAL read_buffer_size = 131072;            -- 128KB读缓冲
        SET GLOBAL read_rnd_buffer_size = 262144;        -- 256KB随机读缓冲
        
        -- 批量插入优化
        SET GLOBAL bulk_insert_buffer_size = 8388608;    -- 8MB批量插入缓冲
        SET GLOBAL myisam_sort_buffer_size = 67108864;   -- 64MB MyISAM排序缓冲
        """
    
    async def optimize_database_settings(self, engine):
        """应用数据库优化设置"""
        try:
            optimization_sql = self.get_mysql_server_optimization_sql()
            
            # 分解为单个语句执行
            statements = [stmt.strip() for stmt in optimization_sql.split(';') if stmt.strip()]
            
            async with engine.begin() as conn:
                for statement in statements:
                    try:
                        await conn.execute(text(statement))
                        logger.info(f"执行优化语句: {statement[:50]}...")
                    except Exception as e:
                        logger.warning(f"优化语句执行失败: {statement[:50]}... - {e}")
                        # 某些设置可能需要特殊权限，忽略错误继续
                        continue
            
            logger.info("数据库优化设置已应用")
            
        except Exception as e:
            logger.error(f"应用数据库优化失败: {e}")
    
    @staticmethod
    def get_table_optimization_suggestions() -> Dict[str, str]:
        """获取表优化建议"""
        return {
            "user_hotspot_collections": """
                -- 热点收集表优化
                ALTER TABLE user_hotspot_collections 
                ENGINE = InnoDB
                ROW_FORMAT = COMPRESSED
                KEY_BLOCK_SIZE = 8;
                
                -- 分区优化（按月分区）
                ALTER TABLE user_hotspot_collections 
                PARTITION BY RANGE (YEAR(collected_at) * 100 + MONTH(collected_at)) (
                    PARTITION p202501 VALUES LESS THAN (202502),
                    PARTITION p202502 VALUES LESS THAN (202503),
                    PARTITION p202503 VALUES LESS THAN (202504),
                    PARTITION p202504 VALUES LESS THAN (202505),
                    PARTITION p202505 VALUES LESS THAN (202506),
                    PARTITION p202506 VALUES LESS THAN (202507),
                    PARTITION p202507 VALUES LESS THAN (202508),
                    PARTITION p202508 VALUES LESS THAN (202509),
                    PARTITION p202509 VALUES LESS THAN (202510),
                    PARTITION p202510 VALUES LESS THAN (202511),
                    PARTITION p202511 VALUES LESS THAN (202512),
                    PARTITION p202512 VALUES LESS THAN (202601),
                    PARTITION p_future VALUES LESS THAN MAXVALUE
                );
            """,
            
            "game_sessions": """
                -- 游戏会话表优化
                ALTER TABLE game_sessions 
                ENGINE = InnoDB
                ROW_FORMAT = DYNAMIC;
                
                -- 添加复合索引
                CREATE INDEX idx_user_city_time ON game_sessions(user_id, city_id, started_at DESC);
                CREATE INDEX idx_status_time ON game_sessions(status, started_at DESC);
            """,
            
            "users": """
                -- 用户表优化
                ALTER TABLE users 
                ENGINE = InnoDB
                ROW_FORMAT = DYNAMIC;
                
                -- 优化索引
                CREATE INDEX idx_level_exp ON users(level, exp DESC);
                CREATE INDEX idx_last_login ON users(last_login_time DESC);
                CREATE INDEX idx_active_users ON users(is_active, last_login_time DESC);
            """,
            
            "user_login_logs": """
                -- 登录日志表优化（大表）
                ALTER TABLE user_login_logs 
                ENGINE = InnoDB
                ROW_FORMAT = COMPRESSED
                KEY_BLOCK_SIZE = 4;
                
                -- 按月分区
                ALTER TABLE user_login_logs 
                PARTITION BY RANGE (YEAR(login_time) * 100 + MONTH(login_time)) (
                    PARTITION p202501 VALUES LESS THAN (202502),
                    PARTITION p202502 VALUES LESS THAN (202503),
                    PARTITION p202503 VALUES LESS THAN (202504),
                    PARTITION p202504 VALUES LESS THAN (202505),
                    PARTITION p202505 VALUES LESS THAN (202506),
                    PARTITION p202506 VALUES LESS THAN (202507),
                    PARTITION p_future VALUES LESS THAN MAXVALUE
                );
            """
        }
    
    @staticmethod
    def get_query_optimization_tips() -> Dict[str, str]:
        """获取查询优化建议"""
        return {
            "热点收集查询": """
                -- 优化前（慢）
                SELECT * FROM user_hotspot_collections 
                WHERE user_id = ? AND collected_at >= ?
                ORDER BY collected_at DESC;
                
                -- 优化后（快）
                SELECT hotspot_name, hotspot_type, collected_at 
                FROM user_hotspot_collections 
                WHERE user_id = ? AND collected_at >= ?
                ORDER BY collected_at DESC 
                LIMIT 100;
                
                -- 使用覆盖索引
                CREATE INDEX idx_user_time_cover ON user_hotspot_collections(
                    user_id, collected_at DESC, hotspot_name, hotspot_type
                );
            """,
            
            "排行榜查询": """
                -- 优化前（慢）
                SELECT u.nickname, COUNT(*) as total_collections
                FROM users u 
                LEFT JOIN user_hotspot_collections uhc ON u.id = uhc.user_id
                GROUP BY u.id 
                ORDER BY total_collections DESC;
                
                -- 优化后（快）- 使用物化视图或定时统计
                CREATE TABLE user_collection_stats AS
                SELECT user_id, COUNT(*) as total_collections,
                       MAX(collected_at) as last_collection
                FROM user_hotspot_collections 
                GROUP BY user_id;
                
                -- 定时更新统计表
                INSERT INTO user_collection_stats 
                SELECT user_id, COUNT(*), MAX(collected_at)
                FROM user_hotspot_collections 
                WHERE collected_at > ?
                GROUP BY user_id
                ON DUPLICATE KEY UPDATE 
                total_collections = VALUES(total_collections),
                last_collection = VALUES(last_collection);
            """,
            
            "用户进度查询": """
                -- 批量查询优化
                SELECT user_id, thieves_collected, garbage_collected,
                       (thieves_collected + garbage_collected) as total_ammo
                FROM users 
                WHERE id IN (?, ?, ?, ?)  -- 批量查询
                ORDER BY FIELD(id, ?, ?, ?, ?);  -- 保持顺序
            """
        }
    
    async def analyze_slow_queries(self, engine):
        """分析慢查询"""
        try:
            async with engine.begin() as conn:
                # 开启慢查询日志
                await conn.execute(text("SET GLOBAL slow_query_log = 'ON'"))
                await conn.execute(text("SET GLOBAL long_query_time = 1"))  # 1秒以上记录
                await conn.execute(text("SET GLOBAL log_queries_not_using_indexes = 'ON'"))
                
                # 获取慢查询统计
                result = await conn.execute(text("""
                    SELECT COUNT(*) as slow_query_count
                    FROM mysql.slow_log 
                    WHERE start_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
                """))
                
                slow_count = result.scalar()
                logger.info(f"过去24小时慢查询数量: {slow_count}")
                
                # 获取最频繁的慢查询
                result = await conn.execute(text("""
                    SELECT sql_text, COUNT(*) as frequency,
                           AVG(query_time) as avg_time,
                           MAX(query_time) as max_time
                    FROM mysql.slow_log 
                    WHERE start_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
                    GROUP BY sql_text 
                    ORDER BY frequency DESC 
                    LIMIT 10
                """))
                
                slow_queries = result.fetchall()
                for query in slow_queries:
                    logger.warning(f"频繁慢查询: {query.sql_text[:100]}... "
                                  f"频次: {query.frequency}, 平均时间: {query.avg_time}s")
                
        except Exception as e:
            logger.error(f"分析慢查询失败: {e}")
    
    async def get_database_performance_stats(self, engine) -> Dict[str, Any]:
        """获取数据库性能统计"""
        try:
            stats = {}
            
            async with engine.begin() as conn:
                # 连接数统计
                result = await conn.execute(text("SHOW STATUS LIKE 'Threads_connected'"))
                stats['connections'] = result.fetchone()._mapping if result else {}
                
                # InnoDB统计
                result = await conn.execute(text("""
                    SHOW STATUS WHERE Variable_name IN (
                        'Innodb_buffer_pool_read_requests',
                        'Innodb_buffer_pool_reads',
                        'Innodb_rows_inserted',
                        'Innodb_rows_updated',
                        'Innodb_rows_deleted',
                        'Innodb_rows_read'
                    )
                """))
                
                innodb_stats = {row.Variable_name: row.Value for row in result}
                stats['innodb'] = innodb_stats
                
                # 缓冲池命中率
                if innodb_stats.get('Innodb_buffer_pool_read_requests'):
                    hit_rate = (
                        1 - int(innodb_stats['Innodb_buffer_pool_reads']) / 
                        int(innodb_stats['Innodb_buffer_pool_read_requests'])
                    ) * 100
                    stats['buffer_pool_hit_rate'] = f"{hit_rate:.2f}%"
                
                # 表大小统计
                result = await conn.execute(text("""
                    SELECT table_name, 
                           ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
                           table_rows
                    FROM information_schema.tables 
                    WHERE table_schema = DATABASE()
                    ORDER BY (data_length + index_length) DESC
                    LIMIT 10
                """))
                
                stats['largest_tables'] = [dict(row._mapping) for row in result]
                
            return stats
            
        except Exception as e:
            logger.error(f"获取性能统计失败: {e}")
            return {}


# 创建全局优化器实例
mysql_optimizer = MySQLOptimizer() 