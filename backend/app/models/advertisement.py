"""
广告系统数据模型
"""
from sqlalchemy import Column, BigInteger, String, Integer, Boolean, DateTime, Text, Index, UniqueConstraint
from sqlalchemy.sql import func
from app.core.database import Base


class AdWatchRecord(Base):
    """广告观看记录表"""
    __tablename__ = "ad_watch_records"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False, comment="用户ID，软关联")
    ad_type = Column(String(50), nullable=False, comment="广告类型：task_reward, ammo_refill, daily_bonus")
    ad_provider = Column(String(50), default="default", comment="广告提供商")
    reward_type = Column(String(50), comment="奖励类型：gold, diamond, double_reward")
    reward_amount = Column(Integer, default=0, comment="奖励数量")
    reward_multiplier = Column(Integer, default=1, comment="奖励倍数")
    is_rewarded = Column(<PERSON>olean, default=False, comment="是否已发放奖励")
    watch_duration = Column(Integer, default=0, comment="观看时长(秒)")
    ip_address = Column(String(45), comment="IP地址")
    user_agent = Column(Text, comment="用户代理")
    created_at = Column(DateTime, server_default=func.now(), comment="观看时间")
    rewarded_at = Column(DateTime, comment="奖励发放时间")
    
    # 索引
    __table_args__ = (
        Index("idx_user_ad_type", "user_id", "ad_type"),
        Index("idx_user_date", "user_id", "created_at"),
        Index("idx_ad_type_date", "ad_type", "created_at"),
    )
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "ad_type": self.ad_type,
            "ad_provider": self.ad_provider,
            "reward_type": self.reward_type,
            "reward_amount": self.reward_amount,
            "reward_multiplier": self.reward_multiplier,
            "is_rewarded": self.is_rewarded,
            "watch_duration": self.watch_duration,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "rewarded_at": self.rewarded_at.isoformat() if self.rewarded_at else None
        }


class UserAdLimit(Base):
    """用户广告限制表"""
    __tablename__ = "user_ad_limits"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False, comment="用户ID，软关联")
    ad_type = Column(String(50), nullable=False, comment="广告类型")
    date = Column(String(10), nullable=False, comment="日期 YYYY-MM-DD")
    watch_count = Column(Integer, default=0, comment="当日观看次数")
    max_count = Column(Integer, default=5, comment="当日最大观看次数")
    last_watch_time = Column(DateTime, comment="最后观看时间")
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # 唯一约束和索引
    __table_args__ = (
        UniqueConstraint("user_id", "ad_type", "date", name="uk_user_ad_date"),
        Index("idx_user_date_limit", "user_id", "date"),
    )
    
    def to_dict(self):
        """转换为字典"""
        return {
            "user_id": self.user_id,
            "ad_type": self.ad_type,
            "date": self.date,
            "watch_count": self.watch_count,
            "max_count": self.max_count,
            "remaining_count": max(0, self.max_count - self.watch_count),
            "last_watch_time": self.last_watch_time.isoformat() if self.last_watch_time else None
        }


# PRD中没有这些复杂的每日任务进度、登录奖励、抽奖转盘系统，已删除
