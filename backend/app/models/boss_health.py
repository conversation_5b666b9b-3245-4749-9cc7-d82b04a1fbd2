"""
BOSS血量系统模型
"""
from sqlalchemy import Column, String, Integer, DateTime, Boolean, BigInteger, Text
from datetime import datetime
import uuid

from app.core.database import Base


class BossHealth(Base):
    """BOSS血量记录表"""
    __tablename__ = "boss_health"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="记录ID")
    user_id = Column(BigInteger, nullable=False, comment="用户ID")
    session_id = Column(String(100), nullable=False, comment="游戏会话ID")
    city_id = Column(String(50), nullable=False, comment="城市ID")
    level_type = Column(String(20), nullable=False, comment="关卡类型(thief/rubbish/monument)")
    
    # BOSS血量相关
    current_health = Column(Integer, default=100, comment="当前血量百分比")
    max_health = Column(Integer, default=100, comment="最大血量")
    boss_phase = Column(String(20), default="arrogant", comment="BOSS阶段：arrogant/angry/begging/defeated")
    
    # 收集进度
    total_thieves = Column(Integer, default=0, comment="小偷总数")
    caught_thieves = Column(Integer, default=0, comment="已抓小偷数")
    total_rubbish = Column(Integer, default=0, comment="垃圾总数")
    cleaned_rubbish = Column(Integer, default=0, comment="已清理垃圾数")
    total_questions = Column(Integer, default=0, comment="问答总数")
    answered_questions = Column(Integer, default=0, comment="已回答问题数")
    
    # 状态
    is_completed = Column(Boolean, default=False, comment="是否已通关")
    gold_chest_awarded = Column(Boolean, default=False, comment="是否已获得金宝箱")
    
    # 时间记录
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    completed_at = Column(DateTime, nullable=True, comment="通关时间")

    def __repr__(self):
        return f"<BossHealth(user_id={self.user_id}, health={self.current_health}%, phase={self.boss_phase})>"

    def get_boss_dialogue(self):
        """获取当前阶段的BOSS对话"""
        dialogues = {
            "arrogant": "你这个失败者，永远无法阻止我们！",
            "angry": "可恶！我要派出更多的手下！", 
            "begging": "求你手下留情，我们会改邪归正的！",
            "defeated": "算你厉害，我们先撤，但我还会回来的！"
        }
        return dialogues.get(self.boss_phase, "")

    def update_health_by_action(self, action_type: str, count: int = 1):
        """根据行为更新BOSS血量"""
        damage_map = {
            "thief": 2,  # 每个小偷-2%血量
            "rubbish": 1,  # 每个垃圾-1%血量
            "monument": 10  # 每题正确-10%血量
        }
        
        damage = damage_map.get(action_type, 0) * count
        self.current_health = max(0, self.current_health - damage)
        
        # 更新BOSS阶段
        if self.current_health <= 0:
            self.boss_phase = "defeated"
            self.is_completed = True
            self.completed_at = datetime.utcnow()
        elif self.current_health <= 20:
            self.boss_phase = "begging"
        elif self.current_health <= 50:
            self.boss_phase = "angry"
        else:
            self.boss_phase = "arrogant"

    def get_completion_percentage(self):
        """获取总体完成百分比"""
        total_progress = 0
        completed_progress = 0
        
        if self.total_thieves > 0:
            total_progress += self.total_thieves
            completed_progress += self.caught_thieves
            
        if self.total_rubbish > 0:
            total_progress += self.total_rubbish
            completed_progress += self.cleaned_rubbish
            
        if self.total_questions > 0:
            total_progress += self.total_questions
            completed_progress += self.answered_questions
            
        return (completed_progress / total_progress * 100) if total_progress > 0 else 0


class BossDialogue(Base):
    """BOSS对话记录表"""
    __tablename__ = "boss_dialogues"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="对话ID")
    boss_health_id = Column(String(36), nullable=False, comment="BOSS血量记录ID")
    phase = Column(String(20), nullable=False, comment="对话阶段")
    dialogue_text = Column(Text, nullable=False, comment="对话内容")
    triggered_at = Column(DateTime, default=datetime.utcnow, comment="触发时间")

    def __repr__(self):
        return f"<BossDialogue(phase={self.phase}, text={self.dialogue_text[:50]})>"