"""
文化问答系统模型
"""
from sqlalchemy import Column, String, Integer, DateTime, Boolean, BigInteger, Text, JSON
from datetime import datetime
import uuid

from app.core.database import Base


class CulturalQuiz(Base):
    """文化问答题库表"""
    __tablename__ = "cultural_quiz"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="问题ID")
    quiz_code = Column(String(50), unique=True, nullable=False, comment="问题编码")
    city_id = Column(String(50), nullable=False, comment="所属城市ID")
    heritage_type = Column(String(20), nullable=False, comment="遗产类型：world/national/local")
    
    # 问题内容
    question = Column(Text, nullable=False, comment="问题内容")
    options = Column(JSON, nullable=False, comment="选项列表")
    correct_answer = Column(String(10), nullable=False, comment="正确答案(A/B/C/D)")
    explanation = Column(Text, nullable=True, comment="答案解释")
    
    # 难度和分类
    difficulty = Column(String(10), default="medium", comment="难度：easy/medium/hard")
    category = Column(String(50), nullable=False, comment="分类：世界遗产/国家文物/地方特色")
    tags = Column(JSON, nullable=True, comment="标签列表")
    
    # 教育价值
    cultural_value = Column(Text, nullable=True, comment="文化价值描述")
    knowledge_points = Column(JSON, nullable=True, comment="知识点列表")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<CulturalQuiz(code={self.quiz_code}, category={self.category}, difficulty={self.difficulty})>"


class QuizAnswer(Base):
    """用户问答记录表"""
    __tablename__ = "quiz_answers"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="记录ID")
    user_id = Column(BigInteger, nullable=False, comment="用户ID")
    quiz_id = Column(String(36), nullable=False, comment="问题ID")
    session_id = Column(String(100), nullable=False, comment="游戏会话ID")
    
    # 答题信息
    user_answer = Column(String(10), nullable=False, comment="用户答案")
    is_correct = Column(Boolean, nullable=False, comment="是否正确")
    answer_time = Column(Integer, nullable=True, comment="答题时间(秒)")
    
    # 奖励信息
    experience_gained = Column(Integer, default=0, comment="获得经验值")
    artifact_gained = Column(String(100), nullable=True, comment="获得图鉴")
    treasure_box_dropped = Column(Boolean, default=False, comment="是否掉落宝箱")
    
    # 时间记录
    answered_at = Column(DateTime, default=datetime.utcnow, comment="答题时间")

    def __repr__(self):
        return f"<QuizAnswer(user_id={self.user_id}, correct={self.is_correct}, exp={self.experience_gained})>"


class CulturalArtifact(Base):
    """文化图鉴表"""
    __tablename__ = "cultural_artifacts"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="图鉴ID")
    artifact_code = Column(String(50), unique=True, nullable=False, comment="图鉴编码")
    city_id = Column(String(50), nullable=False, comment="所属城市ID")
    
    # 图鉴信息
    name = Column(String(100), nullable=False, comment="名称")
    name_en = Column(String(100), nullable=True, comment="英文名称")
    category = Column(String(20), nullable=False, comment="类别：非遗技艺/民俗文化/历史人物/特色美食")
    rarity = Column(String(10), default="common", comment="稀有度：common/rare/epic")
    
    # 文化内容
    description = Column(Text, nullable=False, comment="描述")
    cultural_background = Column(Text, nullable=True, comment="文化背景")
    historical_significance = Column(Text, nullable=True, comment="历史意义")
    
    # 媒体资源
    image_url = Column(String(255), nullable=True, comment="图片URL")
    audio_url = Column(String(255), nullable=True, comment="音频URL")
    video_url = Column(String(255), nullable=True, comment="视频URL")
    
    # 获取方式
    unlock_condition = Column(String(100), nullable=True, comment="解锁条件")
    drop_rate = Column(Integer, default=100, comment="掉落概率(‰)")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<CulturalArtifact(code={self.artifact_code}, name={self.name}, rarity={self.rarity})>"


class UserArtifactCollection(Base):
    """用户图鉴收集表"""
    __tablename__ = "user_artifact_collections"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="收集ID")
    user_id = Column(BigInteger, nullable=False, comment="用户ID")
    artifact_id = Column(String(36), nullable=False, comment="图鉴ID")
    
    # 收集信息
    obtained_at = Column(DateTime, default=datetime.utcnow, comment="获得时间")
    obtained_from = Column(String(50), nullable=True, comment="获得来源：quiz/treasure_box/task")
    quantity = Column(Integer, default=1, comment="数量")
    
    # 状态
    is_new = Column(Boolean, default=True, comment="是否新获得")
    last_viewed_at = Column(DateTime, nullable=True, comment="最后查看时间")

    def __repr__(self):
        return f"<UserArtifactCollection(user_id={self.user_id}, artifact_id={self.artifact_id}, qty={self.quantity})>"


class MonumentRestoration(Base):
    """古迹修复记录表"""
    __tablename__ = "monument_restorations"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="修复ID")
    user_id = Column(BigInteger, nullable=False, comment="用户ID")
    monument_id = Column(String(50), nullable=False, comment="古迹ID")
    city_id = Column(String(50), nullable=False, comment="城市ID")
    
    # 修复进度
    total_questions = Column(Integer, default=0, comment="总问题数")
    answered_questions = Column(Integer, default=0, comment="已答问题数")
    correct_answers = Column(Integer, default=0, comment="正确答案数")
    restoration_progress = Column(Integer, default=0, comment="修复进度百分比")
    
    # 状态
    is_completed = Column(Boolean, default=False, comment="是否完成修复")
    completed_at = Column(DateTime, nullable=True, comment="完成时间")
    
    # 奖励
    total_experience = Column(Integer, default=0, comment="总获得经验")
    artifacts_gained = Column(JSON, nullable=True, comment="获得图鉴列表")
    
    # 时间记录
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<MonumentRestoration(user_id={self.user_id}, monument={self.monument_id}, progress={self.restoration_progress}%)>"

    def calculate_progress(self):
        """计算修复进度"""
        if self.total_questions > 0:
            self.restoration_progress = int((self.answered_questions / self.total_questions) * 100)
            if self.restoration_progress >= 100:
                self.is_completed = True
                self.completed_at = datetime.utcnow()