"""
每日任务系统模型
"""
from sqlalchemy import Column, String, Integer, DateTime, Boolean, BigInteger, Text, JSON, Enum
from datetime import datetime, date
import uuid
import enum

from app.core.database import Base


class TaskType(str, enum.Enum):
    """任务类型枚举"""
    CATCH_THIEF = "catch_thief"      # 抓捕小偷
    CLEAN_RUBBISH = "clean_rubbish"  # 清理垃圾
    CULTURAL_QUIZ = "cultural_quiz"  # 文化学习
    PERFECT_CLEAR = "perfect_clear"  # 完美通关


class TaskStatus(str, enum.Enum):
    """任务状态枚举"""
    ACTIVE = "active"        # 进行中
    COMPLETED = "completed"  # 已完成
    EXPIRED = "expired"      # 已过期


class DailyTaskTemplate(Base):
    """每日任务模板表"""
    __tablename__ = "daily_task_templates"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="模板ID")
    task_code = Column(String(50), unique=True, nullable=False, comment="任务编码")
    task_type = Column(Enum(TaskType), nullable=False, comment="任务类型")
    
    # 任务配置
    name = Column(String(100), nullable=False, comment="任务名称")
    name_en = Column(String(100), nullable=True, comment="英文名称")
    description = Column(Text, nullable=False, comment="任务描述")
    description_en = Column(Text, nullable=True, comment="英文描述")
    
    # 目标配置
    target_amounts = Column(JSON, nullable=False, comment="目标数量配置[15,30,60]")
    reward_experiences = Column(JSON, nullable=False, comment="经验奖励配置[50,120,300]")
    estimated_times = Column(JSON, nullable=False, comment="预估完成时间(分钟)[15,30,60]")
    
    # 难度和要求
    difficulty_level = Column(Integer, default=1, comment="难度等级1-3")
    required_level = Column(Integer, default=1, comment="最低等级要求")
    city_requirement = Column(String(50), nullable=True, comment="城市要求")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    display_order = Column(Integer, default=0, comment="显示顺序")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<DailyTaskTemplate(code={self.task_code}, type={self.task_type}, difficulty={self.difficulty_level})>"


class UserDailyTask(Base):
    """用户每日任务表"""
    __tablename__ = "user_daily_tasks"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="任务ID")
    user_id = Column(BigInteger, nullable=False, comment="用户ID")
    template_id = Column(String(36), nullable=False, comment="任务模板ID")
    task_date = Column(DateTime, nullable=False, comment="任务日期")
    
    # 任务信息
    task_type = Column(Enum(TaskType), nullable=False, comment="任务类型")
    task_name = Column(String(100), nullable=False, comment="任务名称")
    task_description = Column(Text, nullable=False, comment="任务描述")
    
    # 进度跟踪
    target_amount = Column(Integer, nullable=False, comment="目标数量")
    current_progress = Column(Integer, default=0, comment="当前进度")
    completion_rate = Column(Integer, default=0, comment="完成百分比")
    
    # 奖励信息
    base_experience = Column(Integer, nullable=False, comment="基础经验奖励")
    actual_experience = Column(Integer, default=0, comment="实际获得经验")
    is_ad_doubled = Column(Boolean, default=False, comment="是否观看广告翻倍")
    
    # 状态
    status = Column(Enum(TaskStatus), default=TaskStatus.ACTIVE, comment="任务状态")
    started_at = Column(DateTime, default=datetime.utcnow, comment="开始时间")
    completed_at = Column(DateTime, nullable=True, comment="完成时间")
    claimed_at = Column(DateTime, nullable=True, comment="领取时间")
    
    # 统计
    attempts_count = Column(Integer, default=0, comment="尝试次数")
    time_spent = Column(Integer, default=0, comment="花费时间(秒)")

    def __repr__(self):
        return f"<UserDailyTask(user_id={self.user_id}, type={self.task_type}, progress={self.current_progress}/{self.target_amount})>"

    def update_progress(self, amount: int = 1):
        """更新任务进度"""
        self.current_progress = min(self.target_amount, self.current_progress + amount)
        self.completion_rate = int((self.current_progress / self.target_amount) * 100)
        
        if self.current_progress >= self.target_amount:
            self.status = TaskStatus.COMPLETED
            self.completed_at = datetime.utcnow()

    def can_claim_reward(self):
        """检查是否可以领取奖励"""
        return self.status == TaskStatus.COMPLETED and self.claimed_at is None

    def claim_reward(self, is_ad_doubled: bool = False):
        """领取奖励"""
        if self.can_claim_reward():
            self.actual_experience = self.base_experience * (2 if is_ad_doubled else 1)
            self.is_ad_doubled = is_ad_doubled
            self.claimed_at = datetime.utcnow()
            return self.actual_experience
        return 0

    def get_efficiency_score(self):
        """获取效率评分（经验/分钟）"""
        if self.time_spent > 0:
            return round((self.actual_experience / (self.time_spent / 60)), 2)
        return 0

    def is_expired(self):
        """检查任务是否过期"""
        if self.task_date.date() < date.today():
            self.status = TaskStatus.EXPIRED
            return True
        return False


class TaskProgressLog(Base):
    """任务进度日志表"""
    __tablename__ = "task_progress_logs"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="日志ID")
    user_id = Column(BigInteger, nullable=False, comment="用户ID")
    task_id = Column(String(36), nullable=False, comment="任务ID")
    
    # 进度信息
    action_type = Column(String(20), nullable=False, comment="行为类型")
    progress_before = Column(Integer, default=0, comment="行为前进度")
    progress_after = Column(Integer, default=0, comment="行为后进度")
    progress_delta = Column(Integer, default=0, comment="进度变化")
    
    # 上下文信息
    session_id = Column(String(100), nullable=True, comment="游戏会话ID")
    city_id = Column(String(50), nullable=True, comment="城市ID")
    level_type = Column(String(20), nullable=True, comment="关卡类型")
    
    # 时间记录
    created_at = Column(DateTime, default=datetime.utcnow, comment="记录时间")

    def __repr__(self):
        return f"<TaskProgressLog(user_id={self.user_id}, action={self.action_type}, delta={self.progress_delta})>"


class TaskRewardHistory(Base):
    """任务奖励历史表"""
    __tablename__ = "task_reward_histories"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="历史ID")
    user_id = Column(BigInteger, nullable=False, comment="用户ID")
    task_id = Column(String(36), nullable=False, comment="任务ID")
    
    # 奖励信息
    experience_gained = Column(Integer, default=0, comment="获得经验")
    was_doubled = Column(Boolean, default=False, comment="是否翻倍")
    ad_watched = Column(Boolean, default=False, comment="是否观看广告")
    
    # 任务信息
    task_type = Column(Enum(TaskType), nullable=False, comment="任务类型")
    task_difficulty = Column(Integer, default=1, comment="任务难度")
    completion_time = Column(Integer, default=0, comment="完成时间(秒)")
    
    # 时间记录
    rewarded_at = Column(DateTime, default=datetime.utcnow, comment="奖励时间")

    def __repr__(self):
        return f"<TaskRewardHistory(user_id={self.user_id}, exp={self.experience_gained}, doubled={self.was_doubled})>"