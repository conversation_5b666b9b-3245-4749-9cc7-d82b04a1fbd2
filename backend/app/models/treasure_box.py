"""
宝箱系统模型
"""
from sqlalchemy import Column, String, Integer, DateTime, Boolean, BigInteger, Text, JSON, Enum
from datetime import datetime, timedelta
import uuid
import enum
import random

from app.core.database import Base


class TreasureBoxType(str, enum.Enum):
    """宝箱类型枚举"""
    COPPER = "copper"    # 铜宝箱
    SILVER = "silver"    # 银宝箱
    GOLD = "gold"        # 金宝箱


class BoxStatus(str, enum.Enum):
    """宝箱状态枚举"""
    PENDING = "pending"      # 待开启
    OPENED = "opened"        # 已开启
    AD_OPENED = "ad_opened"  # 广告开启


class DropSource(str, enum.Enum):
    """掉落来源枚举"""
    CATCH_THIEF = "catch_thief"      # 抓捕小偷
    CLEAN_RUBBISH = "clean_rubbish"  # 清理垃圾
    CULTURAL_QUIZ = "cultural_quiz"  # 古迹问答
    LEVEL_COMPLETE = "level_complete" # 关卡通关
    DAILY_TASK = "daily_task"        # 每日任务


class TreasureBoxConfig(Base):
    """宝箱配置表"""
    __tablename__ = "treasure_box_configs"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="配置ID")
    box_type = Column(Enum(TreasureBoxType), nullable=False, comment="宝箱类型")
    
    # 掉落配置
    drop_source = Column(Enum(DropSource), nullable=False, comment="掉落来源")
    drop_rate = Column(Integer, nullable=False, comment="掉落概率(千分比)")
    daily_limit = Column(Integer, default=0, comment="每日掉落上限(0=无限制)")
    
    # 基础奖励配置
    base_stamina = Column(Integer, default=0, comment="基础体力奖励")
    base_items = Column(JSON, nullable=True, comment="基础道具奖励")
    base_artifacts = Column(JSON, nullable=True, comment="基础图鉴奖励")
    
    # 广告翻倍奖励
    ad_stamina_multiplier = Column(Integer, default=2, comment="广告体力倍数")
    ad_items_multiplier = Column(Integer, default=2, comment="广告道具倍数")
    ad_artifacts_multiplier = Column(Integer, default=2, comment="广告图鉴倍数")
    
    # 稀有奖励配置
    rare_item_chance = Column(Integer, default=0, comment="稀有道具概率(千分比)")
    rare_items = Column(JSON, nullable=True, comment="稀有道具列表")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<TreasureBoxConfig(type={self.box_type}, source={self.drop_source}, rate={self.drop_rate}‰)>"

    def calculate_drop(self):
        """计算是否掉落宝箱"""
        return random.randint(1, 1000) <= self.drop_rate

    def get_base_rewards(self):
        """获取基础奖励"""
        return {
            "stamina": self.base_stamina,
            "items": self.base_items or {},
            "artifacts": self.base_artifacts or {}
        }

    def get_ad_rewards(self):
        """获取广告翻倍奖励"""
        base_rewards = self.get_base_rewards()
        return {
            "stamina": base_rewards["stamina"] * self.ad_stamina_multiplier,
            "items": {k: v * self.ad_items_multiplier for k, v in base_rewards["items"].items()},
            "artifacts": {k: v * self.ad_artifacts_multiplier for k, v in base_rewards["artifacts"].items()}
        }


class UserTreasureBox(Base):
    """用户宝箱表"""
    __tablename__ = "user_treasure_boxes"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="宝箱ID")
    user_id = Column(BigInteger, nullable=False, comment="用户ID")  
    box_type = Column(Enum(TreasureBoxType), nullable=False, comment="宝箱类型")
    
    # 获得信息
    drop_source = Column(Enum(DropSource), nullable=False, comment="掉落来源")
    source_context = Column(JSON, nullable=True, comment="来源上下文")
    obtained_at = Column(DateTime, default=datetime.utcnow, comment="获得时间")
    
    # 状态
    status = Column(Enum(BoxStatus), default=BoxStatus.PENDING, comment="宝箱状态")
    opened_at = Column(DateTime, nullable=True, comment="开启时间")
    
    # 奖励内容
    stamina_reward = Column(Integer, default=0, comment="体力奖励")
    items_reward = Column(JSON, nullable=True, comment="道具奖励")
    artifacts_reward = Column(JSON, nullable=True, comment="图鉴奖励")
    was_ad_doubled = Column(Boolean, default=False, comment="是否广告翻倍")
    
    # 过期时间
    expires_at = Column(DateTime, nullable=True, comment="过期时间")

    def __repr__(self):
        return f"<UserTreasureBox(user_id={self.user_id}, type={self.box_type}, status={self.status})>"

    def is_expired(self):
        """检查是否过期"""
        if self.expires_at and datetime.utcnow() > self.expires_at:
            return True
        return False

    def can_open(self):
        """检查是否可以开启"""
        return self.status == BoxStatus.PENDING and not self.is_expired()

    def open_box(self, config: TreasureBoxConfig, use_ad: bool = False):
        """开启宝箱"""
        if not self.can_open():
            return None
            
        if use_ad:
            rewards = config.get_ad_rewards()
            self.status = BoxStatus.AD_OPENED
            self.was_ad_doubled = True
        else:
            rewards = config.get_base_rewards()
            self.status = BoxStatus.OPENED
            self.was_ad_doubled = False
            
        self.stamina_reward = rewards["stamina"]
        self.items_reward = rewards["items"]
        self.artifacts_reward = rewards["artifacts"]
        self.opened_at = datetime.utcnow()
        
        return rewards

    def get_time_until_expiry(self):
        """获取距离过期的时间(秒)"""
        if not self.expires_at:
            return None
        time_left = (self.expires_at - datetime.utcnow()).total_seconds()
        return max(0, int(time_left))


class TreasureBoxDropLog(Base):
    """宝箱掉落日志表"""
    __tablename__ = "treasure_box_drop_logs"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="日志ID")
    user_id = Column(BigInteger, nullable=False, comment="用户ID")
    box_type = Column(Enum(TreasureBoxType), nullable=False, comment="宝箱类型")
    drop_source = Column(Enum(DropSource), nullable=False, comment="掉落来源")
    
    # 掉落条件
    drop_roll = Column(Integer, nullable=False, comment="掉落随机数")
    drop_rate = Column(Integer, nullable=False, comment="掉落概率")
    drop_success = Column(Boolean, nullable=False, comment="是否掉落成功")
    
    # 上下文信息
    session_id = Column(String(100), nullable=True, comment="游戏会话ID")
    city_id = Column(String(50), nullable=True, comment="城市ID")
    level_type = Column(String(20), nullable=True, comment="关卡类型")
    
    # 时间记录
    created_at = Column(DateTime, default=datetime.utcnow, comment="掉落时间")

    def __repr__(self):
        return f"<TreasureBoxDropLog(user_id={self.user_id}, type={self.box_type}, success={self.drop_success})>"


class BoxOpenRecord(Base):
    """宝箱开启记录表"""
    __tablename__ = "box_open_records"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="记录ID")
    user_id = Column(BigInteger, nullable=False, comment="用户ID")
    box_id = Column(String(36), nullable=False, comment="宝箱ID")
    box_type = Column(Enum(TreasureBoxType), nullable=False, comment="宝箱类型")
    
    # 开启信息
    open_method = Column(String(20), nullable=False, comment="开启方式：normal/ad")
    stamina_gained = Column(Integer, default=0, comment="获得体力")
    items_gained = Column(JSON, nullable=True, comment="获得道具")
    artifacts_gained = Column(JSON, nullable=True, comment="获得图鉴")
    
    # 统计信息
    total_value = Column(Integer, default=0, comment="总价值评估")
    was_lucky = Column(Boolean, default=False, comment="是否幸运掉落")
    
    # 时间记录
    opened_at = Column(DateTime, default=datetime.utcnow, comment="开启时间")

    def __repr__(self):
        return f"<BoxOpenRecord(user_id={self.user_id}, type={self.box_type}, method={self.open_method})>"


class UserBoxStatistics(Base):
    """用户宝箱统计表"""
    __tablename__ = "user_box_statistics"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="统计ID")
    user_id = Column(BigInteger, nullable=False, unique=True, comment="用户ID")
    
    # 宝箱获得统计
    total_boxes_obtained = Column(Integer, default=0, comment="总获得宝箱数")
    copper_boxes_obtained = Column(Integer, default=0, comment="铜宝箱获得数")
    silver_boxes_obtained = Column(Integer, default=0, comment="银宝箱获得数")
    gold_boxes_obtained = Column(Integer, default=0, comment="金宝箱获得数")
    
    # 宝箱开启统计
    total_boxes_opened = Column(Integer, default=0, comment="总开启宝箱数")
    copper_boxes_opened = Column(Integer, default=0, comment="铜宝箱开启数")
    silver_boxes_opened = Column(Integer, default=0, comment="银宝箱开启数")
    gold_boxes_opened = Column(Integer, default=0, comment="金宝箱开启数")
    
    # 广告统计
    total_ad_opens = Column(Integer, default=0, comment="广告开启总数")
    total_free_opens = Column(Integer, default=0, comment="免费开启总数")
    
    # 奖励统计
    total_stamina_from_boxes = Column(Integer, default=0, comment="宝箱获得体力总数")
    total_items_from_boxes = Column(Integer, default=0, comment="宝箱获得道具总数")
    total_artifacts_from_boxes = Column(Integer, default=0, comment="宝箱获得图鉴总数")
    
    # 时间记录
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<UserBoxStatistics(user_id={self.user_id}, total_opened={self.total_boxes_opened})>"

    def update_obtain_stats(self, box_type: TreasureBoxType):
        """更新获得统计"""
        self.total_boxes_obtained += 1
        if box_type == TreasureBoxType.COPPER:
            self.copper_boxes_obtained += 1
        elif box_type == TreasureBoxType.SILVER:
            self.silver_boxes_obtained += 1
        elif box_type == TreasureBoxType.GOLD:
            self.gold_boxes_obtained += 1

    def update_open_stats(self, box_type: TreasureBoxType, is_ad: bool, rewards: dict):
        """更新开启统计"""
        self.total_boxes_opened += 1
        if box_type == TreasureBoxType.COPPER:
            self.copper_boxes_opened += 1
        elif box_type == TreasureBoxType.SILVER:
            self.silver_boxes_opened += 1
        elif box_type == TreasureBoxType.GOLD:
            self.gold_boxes_opened += 1
            
        if is_ad:
            self.total_ad_opens += 1
        else:
            self.total_free_opens += 1
            
        # 更新奖励统计
        self.total_stamina_from_boxes += rewards.get("stamina", 0)
        self.total_items_from_boxes += sum(rewards.get("items", {}).values())
        self.total_artifacts_from_boxes += sum(rewards.get("artifacts", {}).values())

    def get_open_rate(self):
        """获取开启率"""
        if self.total_boxes_obtained > 0:
            return round((self.total_boxes_opened / self.total_boxes_obtained) * 100, 2)
        return 0

    def get_ad_rate(self):
        """获取广告开启率"""
        if self.total_boxes_opened > 0:
            return round((self.total_ad_opens / self.total_boxes_opened) * 100, 2)
        return 0