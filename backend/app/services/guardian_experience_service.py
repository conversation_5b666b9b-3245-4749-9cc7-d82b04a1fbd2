"""
守护者经验值和等级系统服务
根据PRD需求实现完整的经验值和等级管理
"""
import asyncio
import logging
import math
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from sqlalchemy.orm import selectinload

from app.core.database import get_db
from app.core.redis_client import redis_manager
from app.models.user import User
from app.models.daily_task import UserDailyTask, TaskProgressLog
from app.services.experience_cache_service import experience_cache_service

logger = logging.getLogger(__name__)


class GuardianExperienceService:
    """守护者经验值系统服务"""
    
    def __init__(self):
        self.exp_efficiency_cache = {}
        self.level_requirements = {
            1: 100, 2: 150, 3: 250, 4: 500, 5: 1000,
            6: 2000, 7: 4000, 8: 7000, 9: 10000
        }
    
    # ==================== 经验值获取 ====================
    
    async def gain_experience(
        self, 
        user_id: int, 
        experience_amount: int, 
        source: str, 
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        用户获得经验值
        Args:
            user_id: 用户ID
            experience_amount: 经验值数量
            source: 经验来源 (thief/rubbish/monument/task/passive)
            context: 上下文信息
        """
        try:
            async for db in get_db():
                # 获取用户信息
                user = await self._get_user_with_efficiency(db, user_id)
                if not user:
                    return {"error": "用户不存在", "code": "USER_NOT_FOUND"}
                
                # 计算经验获取效率
                efficiency = user.get_experience_efficiency()
                actual_experience = int(experience_amount * efficiency)
                
                # 记录经验获取
                experience_log = {
                    "user_id": user_id,
                    "source": source,
                    "base_experience": experience_amount,
                    "efficiency": efficiency,
                    "actual_experience": actual_experience,
                    "context": context or {},
                    "timestamp": datetime.utcnow()
                }
                
                # 更新用户经验
                old_level = user.guardian_level
                old_exp = user.guardian_exp
                old_total_exp = user.total_exp
                
                user.guardian_exp += actual_experience
                user.total_exp += actual_experience
                
                # 检查升级
                level_up_info = await self._check_and_process_level_up(user)
                
                # 保存用户信息
                await db.commit()
                
                # 更新缓存
                await self._update_user_cache(user)
                
                # 如果升级了，处理升级奖励
                if level_up_info["leveled_up"]:
                    await self._process_level_up_rewards(user, level_up_info)
                
                return {
                    "success": True,
                    "experience_gained": actual_experience,
                    "base_experience": experience_amount,
                    "efficiency": efficiency,
                    "current_exp": user.guardian_exp,
                    "total_exp": user.total_exp,
                    "current_level": user.guardian_level,
                    "leveled_up": level_up_info["leveled_up"],
                    "levels_gained": level_up_info["levels_gained"],
                    "new_passive_rate": user.get_offline_income_rate(),
                    "next_level_requirement": user.calculate_level_up_requirement(),
                    "appearance": user.get_guardian_appearance()
                }
                
        except Exception as e:
            logger.error(f"获得经验值失败 user_id={user_id}, exp={experience_amount}: {e}")
            return {"error": "获得经验失败", "code": "EXPERIENCE_ERROR"}
    
    async def gain_thief_experience(self, user_id: int, thief_count: int = 1, context: Dict = None) -> Dict[str, Any]:
        """抓捕小偷获得经验"""
        base_exp_per_thief = 12  # PRD规定每个小偷12点经验
        total_experience = base_exp_per_thief * thief_count
        
        return await self.gain_experience(
            user_id, 
            total_experience, 
            "catch_thief",
            {**(context or {}), "thief_count": thief_count, "exp_per_thief": base_exp_per_thief}
        )
    
    async def gain_rubbish_experience(self, user_id: int, rubbish_count: int = 1, context: Dict = None) -> Dict[str, Any]:
        """清理垃圾获得经验"""
        base_exp_per_rubbish = 8  # PRD规定每个垃圾8点经验
        total_experience = base_exp_per_rubbish * rubbish_count
        
        return await self.gain_experience(
            user_id,
            total_experience,
            "clean_rubbish", 
            {**(context or {}), "rubbish_count": rubbish_count, "exp_per_rubbish": base_exp_per_rubbish}
        )
    
    async def gain_monument_experience(
        self, 
        user_id: int, 
        correct_answers: int = 1, 
        can_double: bool = True,
        context: Dict = None
    ) -> Dict[str, Any]:
        """古迹问答获得经验"""
        base_exp_per_answer = 35  # PRD规定每题正确35点经验
        total_experience = base_exp_per_answer * correct_answers
        
        return await self.gain_experience(
            user_id,
            total_experience,
            "monument_quiz",
            {
                **(context or {}), 
                "correct_answers": correct_answers, 
                "exp_per_answer": base_exp_per_answer,
                "can_double": can_double
            }
        )
    
    async def gain_level_complete_experience(self, user_id: int, context: Dict = None) -> Dict[str, Any]:
        """关卡通关获得经验"""
        completion_experience = 200  # PRD规定通关200点经验
        
        return await self.gain_experience(
            user_id,
            completion_experience,
            "level_complete",
            {**(context or {}), "completion_bonus": True}
        )
    
    async def gain_task_experience(
        self, 
        user_id: int, 
        task_id: str, 
        base_experience: int,
        is_doubled: bool = False,
        context: Dict = None
    ) -> Dict[str, Any]:
        """任务完成获得经验"""
        actual_experience = base_experience * (2 if is_doubled else 1)
        
        return await self.gain_experience(
            user_id,
            actual_experience,
            "daily_task",
            {
                **(context or {}),
                "task_id": task_id,
                "base_experience": base_experience,
                "is_doubled": is_doubled
            }
        )
    
    # ==================== 被动收益系统 ====================
    
    async def collect_passive_income(self, user_id: int) -> Dict[str, Any]:
        """收集被动收益"""
        try:
            async for db in get_db():
                user = await self._get_user(db, user_id)
                if not user:
                    return {"error": "用户不存在", "code": "USER_NOT_FOUND"}
                
                # 计算离线时间
                now = datetime.utcnow()
                last_collection = user.last_offline_collection or user.created_at or now
                offline_minutes = int((now - last_collection).total_seconds() / 60)
                
                if offline_minutes <= 0:
                    return {
                        "success": True,
                        "offline_minutes": 0,
                        "experience_gained": 0,
                        "income_rate": user.get_offline_income_rate()
                    }
                
                # 限制最大离线收益时间（PRD未明确，设置为12小时）
                max_offline_minutes = 12 * 60  # 12小时
                effective_minutes = min(offline_minutes, max_offline_minutes)
                
                # 计算被动收益
                income_rate = user.get_offline_income_rate()
                passive_experience = income_rate * effective_minutes
                
                # 更新用户信息
                user.last_offline_collection = now
                
                # 获得经验
                result = await self.gain_experience(
                    user_id,
                    passive_experience,
                    "passive_income",
                    {
                        "offline_minutes": offline_minutes,
                        "effective_minutes": effective_minutes,
                        "income_rate": income_rate,
                        "guardian_level": user.guardian_level
                    }
                )
                
                await db.commit()
                
                if result.get("success"):
                    result.update({
                        "offline_minutes": offline_minutes,
                        "effective_minutes": effective_minutes,
                        "income_rate": income_rate,
                        "max_offline_hours": max_offline_minutes // 60
                    })
                
                return result
                
        except Exception as e:
            logger.error(f"收集被动收益失败 user_id={user_id}: {e}")
            return {"error": "收集失败", "code": "PASSIVE_INCOME_ERROR"}
    
    # ==================== 等级系统 ====================
    
    async def _check_and_process_level_up(self, user: User) -> Dict[str, Any]:
        """检查并处理用户升级"""
        levels_gained = 0
        original_level = user.guardian_level
        
        while user.can_level_up():
            if user.level_up():
                levels_gained += 1
            else:
                break
                
        leveled_up = levels_gained > 0
        
        return {
            "leveled_up": leveled_up,
            "levels_gained": levels_gained,
            "original_level": original_level,
            "new_level": user.guardian_level
        }
    
    async def _process_level_up_rewards(self, user: User, level_info: Dict[str, Any]):
        """处理升级奖励"""
        try:
            # 升级时完全恢复体力（PRD要求）
            user.stamina = user.max_stamina
            user.last_stamina_update = datetime.utcnow()
            
            # 更新被动收益速率
            new_income_rate = user.get_offline_income_rate()
            
            # 记录升级事件
            level_up_event = {
                "user_id": user.id,
                "event_type": "level_up",
                "original_level": level_info["original_level"],
                "new_level": level_info["new_level"],
                "levels_gained": level_info["levels_gained"],
                "new_income_rate": new_income_rate,
                "new_appearance": user.get_guardian_appearance(),
                "stamina_restored": user.max_stamina,
                "timestamp": datetime.utcnow()
            }
            
            # 缓存升级事件（可用于推送通知等）
            await redis_manager.lpush(
                f"user:{user.id}:level_up_events",
                str(level_up_event)
            )
            await redis_manager.expire(f"user:{user.id}:level_up_events", 86400)  # 1天过期
            
            logger.info(f"🎉 用户 {user.id} 升级: {level_info['original_level']} -> {level_info['new_level']}")
            
        except Exception as e:
            logger.error(f"处理升级奖励失败 user_id={user.id}: {e}")
    
    # ==================== 数据查询 ====================
    
    async def get_user_experience_info(self, user_id: int) -> Dict[str, Any]:
        """获取用户经验信息"""
        try:
            async for db in get_db():
                user = await self._get_user(db, user_id)
                if not user:
                    return {"error": "用户不存在", "code": "USER_NOT_FOUND"}
                
                # 计算升级进度
                current_level_requirement = user.calculate_level_up_requirement()
                progress_percentage = (user.guardian_exp / current_level_requirement) * 100 if current_level_requirement > 0 else 100
                
                # 计算离线收益预览
                offline_income_rate = user.get_offline_income_rate()
                estimated_hourly_income = offline_income_rate * 60
                
                return {
                    "success": True,
                    "user_id": user_id,
                    "guardian_level": user.guardian_level,
                    "guardian_exp": user.guardian_exp,
                    "total_exp": user.total_exp,
                    "current_stamina": user.stamina,
                    "max_stamina": user.max_stamina,
                    "level_progress": {
                        "current_level": user.guardian_level,
                        "current_exp": user.guardian_exp,
                        "required_exp": current_level_requirement,
                        "progress_percentage": round(progress_percentage, 2),
                        "next_level_appearance": self._get_next_level_appearance(user.guardian_level)
                    },
                    "passive_income": {
                        "rate_per_minute": offline_income_rate,
                        "rate_per_hour": estimated_hourly_income,
                        "last_collection": user.last_offline_collection.isoformat() if user.last_offline_collection else None
                    },
                    "experience_efficiency": user.get_experience_efficiency(),
                    "current_appearance": user.get_guardian_appearance(),
                    "statistics": {
                        "total_thieves_caught": user.total_thieves_caught,
                        "total_rubbish_cleaned": user.total_rubbish_cleaned,
                        "total_monuments_restored": user.total_monuments_restored,
                        "total_quiz_answered": user.total_quiz_answered,
                        "total_quiz_correct": user.total_quiz_correct,
                        "quiz_accuracy": round((user.total_quiz_correct / user.total_quiz_answered * 100), 2) if user.total_quiz_answered > 0 else 0
                    }
                }
                
        except Exception as e:
            logger.error(f"获取用户经验信息失败 user_id={user_id}: {e}")
            return {"error": "获取信息失败", "code": "INFO_ERROR"}
    
    def _get_next_level_appearance(self, current_level: int) -> str:
        """获取下一等级的外观"""
        next_level = current_level + 1
        if next_level <= 3:
            return "初级守卫"
        elif next_level <= 6:
            return "中级守护者"
        elif next_level <= 9:
            return "高级执法者"
        else:
            return "传奇守护者"
    
    # ==================== 体力值系统集成 ====================
    
    async def consume_stamina_for_action(self, user_id: int, action: str, amount: int = 1) -> Dict[str, Any]:
        """为游戏行为消耗体力"""
        stamina_costs = {
            "catch_thief": 1,      # 抓捕小偷消耗1点体力
            "clean_rubbish": 1,    # 清理垃圾消耗1点体力  
            "monument_quiz": 5     # 古迹问答消耗5点体力
        }
        
        stamina_cost = stamina_costs.get(action, 1) * amount
        
        try:
            async for db in get_db():
                user = await self._get_user(db, user_id)
                if not user:
                    return {"error": "用户不存在", "code": "USER_NOT_FOUND"}
                
                # 自然恢复体力
                user.restore_stamina_naturally()
                
                # 检查体力是否足够
                if user.stamina < stamina_cost:
                    return {
                        "error": "体力不足",
                        "code": "INSUFFICIENT_STAMINA",
                        "current_stamina": user.stamina,
                        "required_stamina": stamina_cost,
                        "can_ad_restore": user.can_watch_stamina_ad()
                    }
                
                # 消耗体力
                if user.consume_stamina(stamina_cost):
                    await db.commit()
                    
                    # 更新缓存
                    await self._update_user_cache(user)
                    
                    return {
                        "success": True,
                        "stamina_consumed": stamina_cost,
                        "current_stamina": user.stamina,
                        "experience_efficiency": user.get_experience_efficiency(),
                        "low_stamina_warning": user.stamina < 30
                    }
                
                return {"error": "消耗体力失败", "code": "STAMINA_ERROR"}
                
        except Exception as e:
            logger.error(f"消耗体力失败 user_id={user_id}, action={action}: {e}")
            return {"error": "消耗体力失败", "code": "STAMINA_ERROR"}
    
    # ==================== 辅助方法 ====================
    
    async def _get_user(self, db: AsyncSession, user_id: int) -> Optional[User]:
        """获取用户信息"""
        result = await db.execute(
            select(User).where(User.id == user_id)
        )
        return result.scalar_one_or_none()
    
    async def _get_user_with_efficiency(self, db: AsyncSession, user_id: int) -> Optional[User]:
        """获取用户信息并预计算效率"""
        user = await self._get_user(db, user_id)
        if user:
            # 自然恢复体力
            user.restore_stamina_naturally()
        return user
    
    async def _update_user_cache(self, user: User):
        """更新用户缓存"""
        try:
            user_cache = {
                "guardian_level": user.guardian_level,
                "guardian_exp": user.guardian_exp,
                "total_exp": user.total_exp,
                "stamina": user.stamina,
                "max_stamina": user.max_stamina,
                "offline_income_rate": user.get_offline_income_rate(),
                "experience_efficiency": user.get_experience_efficiency(),
                "appearance": user.get_guardian_appearance(),
                "updated_at": datetime.utcnow().isoformat()
            }
            
            await redis_manager.hmset(
                f"user:{user.id}:guardian_info",
                user_cache
            )
            await redis_manager.expire(f"user:{user.id}:guardian_info", 7200)  # 2小时过期
            
        except Exception as e:
            logger.error(f"更新用户缓存失败 user_id={user.id}: {e}")


# 全局实例
guardian_experience_service = GuardianExperienceService()