"""
定时任务服务
"""
import logging
import asyncio
from datetime import datetime, timedelta
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.services.lottery_service import lottery_service
from app.services.daily_task_service import daily_task_service

logger = logging.getLogger(__name__)


class SchedulerService:
    """定时任务服务"""
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
        
    async def start(self):
        """启动定时任务服务"""
        if self.is_running:
            logger.warning("定时任务服务已经在运行")
            return
            
        # 添加每日0点重置转盘抽奖的任务
        self.scheduler.add_job(
            func=self.reset_daily_lottery,
            trigger=CronTrigger(hour=0, minute=0, second=0),  # 每天0点执行
            id='reset_daily_lottery',
            name='重置每日转盘抽奖',
            replace_existing=True,
            max_instances=1
        )
        
        # 添加每日0点重置每日任务的任务
        self.scheduler.add_job(
            func=self.reset_daily_tasks,
            trigger=CronTrigger(hour=0, minute=1, second=0),  # 每天0点1分执行（避免与转盘重置冲突）
            id='reset_daily_tasks',
            name='重置每日任务',
            replace_existing=True,
            max_instances=1
        )
        
        # 添加清理旧数据的任务（每天2点执行）
        self.scheduler.add_job(
            func=self.cleanup_old_data,
            trigger=CronTrigger(hour=2, minute=0, second=0),
            id='cleanup_old_lottery_data',
            name='清理旧转盘数据',
            replace_existing=True,
            max_instances=1
        )
        
        # 启动调度器
        self.scheduler.start()
        self.is_running = True
        logger.info("定时任务服务启动成功")
        
    async def stop(self):
        """停止定时任务服务"""
        if not self.is_running:
            return
            
        self.scheduler.shutdown(wait=True)
        self.is_running = False
        logger.info("定时任务服务已停止")
        
    async def reset_daily_lottery(self):
        """重置每日转盘抽奖（每天0点执行）"""
        try:
            logger.info("开始执行每日转盘抽奖重置...")
            
            # 获取今天的日期（0点重置当天的抽奖状态）
            today = datetime.now().strftime("%Y-%m-%d")
            
            async for db in get_db():
                result = await lottery_service.reset_daily_prizes(db, today)
                if "error" in result:
                    logger.error(f"重置转盘抽奖失败: {result['error']}")
                else:
                    logger.info(f"转盘抽奖重置成功: 清理了 {result.get('cleared_records', 0)} 条记录")
                break
                
        except Exception as e:
            logger.error(f"执行每日转盘抽奖重置时发生错误: {e}")
            
    async def reset_daily_tasks(self):
        """重置每日任务（每天0点1分执行）"""
        try:
            logger.info("开始重置每日任务...")
            
            async for db in get_db():
                result = await daily_task_service.reset_all_daily_tasks(db)
                if result.get("success"):
                    logger.info(f"每日任务重置成功: 处理了 {result.get('processed_users', 0)} 个用户")
                else:
                    logger.error(f"重置每日任务失败: {result.get('error', '未知错误')}")
                break
                
        except Exception as e:
            logger.error(f"执行每日任务重置时发生错误: {e}")
            
    async def cleanup_old_data(self):
        """清理旧的转盘数据（每天2点执行）"""
        try:
            logger.info("开始清理旧转盘数据...")
            
            async for db in get_db():
                result = await lottery_service.cleanup_old_prize_status(db, days_to_keep=7)
                if "error" in result:
                    logger.error(f"清理旧转盘数据失败: {result['error']}")
                else:
                    logger.info(f"清理旧转盘数据成功: 清理了 {result.get('cleared_records', 0)} 条记录")
                break
                
        except Exception as e:
            logger.error(f"清理旧转盘数据时发生错误: {e}")
            
    def get_job_status(self):
        """获取定时任务状态"""
        if not self.is_running:
            return {"status": "stopped", "jobs": []}
            
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
            
        return {
            "status": "running",
            "jobs": jobs
        }


# 创建全局实例
scheduler_service = SchedulerService() 