"""
宝箱系统服务
根据PRD需求实现完整的宝箱掉落、开启和奖励系统
"""
import asyncio
import logging
import random
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, insert, delete, and_, or_
from sqlalchemy.orm import selectinload

from app.core.database import get_db
from app.core.redis_client import redis_manager
from app.models.treasure_box import (
    TreasureBoxConfig, UserTreasureBox, TreasureBoxDropLog, BoxOpenRecord,
    UserBoxStatistics, TreasureBoxType, BoxStatus, DropSource
)
from app.models.user import User
from app.services.stamina_management_service import stamina_service

logger = logging.getLogger(__name__)


class TreasureBoxService:
    """宝箱系统服务"""
    
    def __init__(self):
        # 宝箱掉落配置（根据PRD优化后的数值）
        self.drop_configs = {
            DropSource.CATCH_THIEF: {
                TreasureBoxType.COPPER: {"rate": 60, "daily_limit": 0},  # 6%掉落率（60‰）
            },
            DropSource.CLEAN_RUBBISH: {
                TreasureBoxType.COPPER: {"rate": 30, "daily_limit": 0},  # 3%掉落率（30‰）
            },
            DropSource.CULTURAL_QUIZ: {
                TreasureBoxType.SILVER: {"rate": 150, "daily_limit": 0}, # 15%掉落率（150‰）
            },
            DropSource.LEVEL_COMPLETE: {
                TreasureBoxType.GOLD: {"rate": 1000, "daily_limit": 0},  # 100%掉落率（必掉）
            }
        }
        
        # 宝箱奖励配置（根据PRD）
        self.reward_configs = {
            TreasureBoxType.COPPER: {
                "base_stamina": 5,
                "base_items": {"magnifier": 1},
                "ad_multiplier": 2,
                "rare_chance": 0  # 铜宝箱无稀有奖励
            },
            TreasureBoxType.SILVER: {
                "base_stamina": 10,
                "base_items": {"artifact": 1},
                "ad_multiplier": 2,
                "rare_chance": 50  # 5%稀有奖励几率
            },
            TreasureBoxType.GOLD: {
                "base_stamina": 0,
                "base_items": {"rare_artifact": 1, "item": 1},
                "ad_multiplier": 2,
                "rare_chance": 100  # 10%稀有奖励几率
            }
        }
    
    # ==================== 宝箱掉落系统 ====================
    
    async def try_drop_treasure_box(
        self,
        user_id: int,
        drop_source: DropSource,
        count: int = 1,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """尝试掉落宝箱"""
        try:
            # 获取掉落配置
            source_config = self.drop_configs.get(drop_source, {})
            if not source_config:
                return {
                    "success": True,
                    "dropped": False,
                    "reason": "该来源不掉落宝箱"
                }
            
            dropped_boxes = []
            
            # 对每个行为都进行掉落判定
            for _ in range(count):
                for box_type, config in source_config.items():
                    drop_result = await self._calculate_drop(
                        user_id, box_type, drop_source, config, context
                    )
                    
                    if drop_result["dropped"]:
                        box = await self._create_treasure_box(
                            user_id, box_type, drop_source, context
                        )
                        if box:
                            dropped_boxes.append({
                                "box_id": box.id,
                                "box_type": box_type.value,
                                "drop_source": drop_source.value
                            })
            
            # 更新用户宝箱统计
            if dropped_boxes:
                await self._update_user_box_statistics(user_id, dropped_boxes, "obtain")
            
            return {
                "success": True,
                "dropped": len(dropped_boxes) > 0,
                "dropped_count": len(dropped_boxes),
                "dropped_boxes": dropped_boxes
            }
            
        except Exception as e:
            logger.error(f"尝试掉落宝箱失败 user_id={user_id}, source={drop_source}: {e}")
            return {"error": "掉落检查失败", "code": "DROP_CHECK_ERROR"}
    
    async def _calculate_drop(
        self,
        user_id: int,
        box_type: TreasureBoxType,
        drop_source: DropSource,
        config: Dict[str, int],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """计算宝箱掉落"""
        try:
            drop_rate = config["rate"]
            daily_limit = config.get("daily_limit", 0)
            
            # 检查每日限制
            if daily_limit > 0:
                today_count = await self._get_today_drop_count(user_id, box_type, drop_source)
                if today_count >= daily_limit:
                    await self._log_drop_attempt(
                        user_id, box_type, drop_source, 0, drop_rate, False, "daily_limit_reached", context
                    )
                    return {"dropped": False, "reason": "达到每日限制"}
            
            # 掉落判定
            roll = random.randint(1, 1000)
            dropped = roll <= drop_rate
            
            # 记录掉落日志
            await self._log_drop_attempt(
                user_id, box_type, drop_source, roll, drop_rate, dropped, "normal", context
            )
            
            return {
                "dropped": dropped,
                "roll": roll,
                "drop_rate": drop_rate,
                "reason": "successful_drop" if dropped else "failed_roll"
            }
            
        except Exception as e:
            logger.error(f"计算宝箱掉落失败: {e}")
            return {"dropped": False, "reason": "calculation_error"}
    
    async def _create_treasure_box(
        self,
        user_id: int,
        box_type: TreasureBoxType,
        drop_source: DropSource,
        context: Dict[str, Any] = None
    ) -> Optional[UserTreasureBox]:
        """创建宝箱"""
        try:
            async for db in get_db():
                # 计算过期时间（根据宝箱类型）
                expiry_hours = {
                    TreasureBoxType.COPPER: 24,  # 铜宝箱24小时过期
                    TreasureBoxType.SILVER: 48,  # 银宝箱48小时过期
                    TreasureBoxType.GOLD: 72     # 金宝箱72小时过期
                }
                
                expires_at = datetime.utcnow() + timedelta(hours=expiry_hours.get(box_type, 24))
                
                treasure_box = UserTreasureBox(
                    user_id=user_id,
                    box_type=box_type,
                    drop_source=drop_source,
                    source_context=context or {},
                    expires_at=expires_at
                )
                
                db.add(treasure_box)
                await db.commit()
                await db.refresh(treasure_box)
                
                # 缓存宝箱信息
                await self._cache_user_boxes(user_id)
                
                logger.info(f"📦 宝箱掉落: user_id={user_id}, type={box_type.value}, source={drop_source.value}")
                
                return treasure_box
                
        except Exception as e:
            logger.error(f"创建宝箱失败: {e}")
            return None
    
    # ==================== 宝箱开启系统 ====================
    
    async def open_treasure_box(
        self,
        user_id: int,
        box_id: str,
        use_ad: bool = False
    ) -> Dict[str, Any]:
        """开启宝箱"""
        try:
            async for db in get_db():
                # 获取宝箱
                box = await self._get_user_treasure_box(db, user_id, box_id)
                if not box:
                    return {"error": "宝箱不存在", "code": "BOX_NOT_FOUND"}
                
                # 检查是否可以开启
                if not box.can_open():
                    return {
                        "error": "宝箱无法开启",
                        "code": "CANNOT_OPEN_BOX",
                        "status": box.status.value,
                        "expired": box.is_expired()
                    }
                
                # 获取奖励配置
                reward_config = self.reward_configs.get(box.box_type)
                if not reward_config:
                    return {"error": "宝箱配置错误", "code": "BOX_CONFIG_ERROR"}
                
                # 计算奖励
                rewards = await self._calculate_box_rewards(reward_config, use_ad)
                
                # 开启宝箱
                box.status = BoxStatus.AD_OPENED if use_ad else BoxStatus.OPENED
                box.was_ad_doubled = use_ad
                box.opened_at = datetime.utcnow()
                box.stamina_reward = rewards["stamina"]
                box.items_reward = rewards["items"]
                box.artifacts_reward = rewards["artifacts"]
                
                # 发放奖励
                reward_result = await self._grant_box_rewards(user_id, rewards)
                
                # 记录开启记录
                await self._record_box_open(db, user_id, box, rewards, use_ad)
                
                # 更新用户宝箱统计
                await self._update_user_box_statistics(user_id, [{"box_type": box.box_type}], "open", use_ad, rewards)
                
                await db.commit()
                
                # 更新缓存
                await self._cache_user_boxes(user_id)
                
                logger.info(f"🎁 开启宝箱: user_id={user_id}, type={box.box_type.value}, ad={use_ad}")
                
                return {
                    "success": True,
                    "box_id": box_id,
                    "box_type": box.box_type.value,
                    "rewards": rewards,
                    "was_ad_doubled": use_ad,
                    "reward_result": reward_result,
                    "opened_at": box.opened_at.isoformat()
                }
                
        except Exception as e:
            logger.error(f"开启宝箱失败 user_id={user_id}, box_id={box_id}: {e}")
            return {"error": "开启宝箱失败", "code": "BOX_OPEN_ERROR"}
    
    async def _calculate_box_rewards(self, config: Dict[str, Any], use_ad: bool) -> Dict[str, Any]:
        """计算宝箱奖励"""
        try:
            multiplier = config["ad_multiplier"] if use_ad else 1
            
            rewards = {
                "stamina": config["base_stamina"] * multiplier,
                "items": {},
                "artifacts": {},
                "rare_items": {}
            }
            
            # 基础道具奖励
            for item_type, base_amount in config["base_items"].items():
                rewards["items"][item_type] = base_amount * multiplier
            
            # 稀有奖励判定
            rare_chance = config.get("rare_chance", 0)
            if rare_chance > 0:
                roll = random.randint(1, 1000)
                if roll <= rare_chance:
                    rewards["rare_items"] = self._get_rare_rewards(multiplier)
            
            return rewards
            
        except Exception as e:
            logger.error(f"计算宝箱奖励失败: {e}")
            return {"stamina": 0, "items": {}, "artifacts": {}, "rare_items": {}}
    
    def _get_rare_rewards(self, multiplier: int = 1) -> Dict[str, int]:
        """获取稀有奖励"""
        rare_items = {
            "radar": 1 * multiplier,
            "premium_artifact": 1 * multiplier,
            "stamina_potion": 2 * multiplier
        }
        
        # 随机选择1-2个稀有奖励
        selected_count = random.randint(1, min(2, len(rare_items)))
        selected_items = dict(random.sample(list(rare_items.items()), selected_count))
        
        return selected_items
    
    async def _grant_box_rewards(self, user_id: int, rewards: Dict[str, Any]) -> Dict[str, Any]:
        """发放宝箱奖励"""
        try:
            grant_results = {}
            
            # 发放体力奖励
            if rewards["stamina"] > 0:
                stamina_result = await stamina_service.special_stamina_recovery(
                    user_id, 
                    rewards["stamina"], 
                    "treasure_box",
                    {"box_reward": True}
                )
                grant_results["stamina"] = stamina_result
            
            # 发放道具奖励
            if rewards["items"]:
                items_result = await self._grant_items(user_id, rewards["items"])
                grant_results["items"] = items_result
            
            # 发放图鉴奖励
            if rewards["artifacts"]:
                artifacts_result = await self._grant_artifacts(user_id, rewards["artifacts"])
                grant_results["artifacts"] = artifacts_result
            
            # 发放稀有奖励
            if rewards["rare_items"]:
                rare_result = await self._grant_items(user_id, rewards["rare_items"])
                grant_results["rare_items"] = rare_result
            
            return grant_results
            
        except Exception as e:
            logger.error(f"发放宝箱奖励失败 user_id={user_id}: {e}")
            return {"error": "奖励发放失败"}
    
    async def _grant_items(self, user_id: int, items: Dict[str, int]) -> Dict[str, Any]:
        """发放道具奖励"""
        try:
            async for db in get_db():
                user = await db.execute(select(User).where(User.id == user_id))
                user = user.scalar_one_or_none()
                
                if not user:
                    return {"error": "用户不存在"}
                
                granted_items = {}
                
                for item_type, amount in items.items():
                    if item_type == "magnifier":
                        user.magnifier_count += amount
                        granted_items["magnifier"] = amount
                    elif item_type == "radar":
                        user.radar_count += amount
                        granted_items["radar"] = amount
                    elif item_type == "stamina_potion":
                        user.stamina_potion_count += amount
                        granted_items["stamina_potion"] = amount
                
                await db.commit()
                
                return {
                    "success": True,
                    "granted_items": granted_items
                }
                
        except Exception as e:
            logger.error(f"发放道具失败 user_id={user_id}: {e}")
            return {"error": "道具发放失败"}
    
    async def _grant_artifacts(self, user_id: int, artifacts: Dict[str, int]) -> Dict[str, Any]:
        """发放图鉴奖励"""
        try:
            # 这里应该调用文化图鉴服务
            # 暂时返回占位结果
            return {
                "success": True,
                "granted_artifacts": artifacts,
                "note": "图鉴系统待实现"
            }
            
        except Exception as e:
            logger.error(f"发放图鉴失败 user_id={user_id}: {e}")
            return {"error": "图鉴发放失败"}
    
    # ==================== 宝箱查询系统 ====================
    
    async def get_user_treasure_boxes(self, user_id: int, include_opened: bool = False) -> Dict[str, Any]:
        """获取用户宝箱列表"""
        try:
            async for db in get_db():
                query = select(UserTreasureBox).where(UserTreasureBox.user_id == user_id)
                
                if not include_opened:
                    query = query.where(UserTreasureBox.status == BoxStatus.PENDING)
                
                query = query.order_by(UserTreasureBox.obtained_at.desc())
                
                result = await db.execute(query)
                boxes = result.scalars().all()
                
                # 检查过期宝箱
                expired_boxes = []
                for box in boxes:
                    if box.is_expired() and box.status == BoxStatus.PENDING:
                        expired_boxes.append(box.id)
                
                # 清理过期宝箱
                if expired_boxes:
                    await self._cleanup_expired_boxes(db, expired_boxes)
                    await db.commit()
                
                # 格式化宝箱信息
                formatted_boxes = []
                for box in boxes:
                    if not box.is_expired() or include_opened:
                        formatted_box = await self._format_box_info(box)
                        formatted_boxes.append(formatted_box)
                
                # 获取统计信息
                stats = await self._get_user_box_stats(db, user_id)
                
                return {
                    "success": True,
                    "user_id": user_id,
                    "boxes": formatted_boxes,
                    "total_boxes": len(formatted_boxes),
                    "pending_boxes": len([b for b in formatted_boxes if b["status"] == "pending"]),
                    "expired_cleaned": len(expired_boxes),
                    "statistics": stats
                }
                
        except Exception as e:
            logger.error(f"获取用户宝箱列表失败 user_id={user_id}: {e}")
            return {"error": "获取宝箱列表失败", "code": "BOX_LIST_ERROR"}
    
    async def get_box_drop_history(self, user_id: int, days: int = 7) -> Dict[str, Any]:
        """获取宝箱掉落历史"""
        try:
            async for db in get_db():
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                
                result = await db.execute(
                    select(TreasureBoxDropLog)
                    .where(
                        TreasureBoxDropLog.user_id == user_id,
                        TreasureBoxDropLog.created_at >= cutoff_date
                    )
                    .order_by(TreasureBoxDropLog.created_at.desc())
                )
                
                logs = result.scalars().all()
                
                # 统计掉落数据
                stats = {
                    "total_attempts": len(logs),
                    "successful_drops": len([l for l in logs if l.drop_success]),
                    "by_source": {},
                    "by_type": {},
                    "daily_breakdown": {}
                }
                
                for log in logs:
                    # 按来源统计
                    source = log.drop_source.value
                    if source not in stats["by_source"]:
                        stats["by_source"][source] = {"attempts": 0, "successes": 0}
                    stats["by_source"][source]["attempts"] += 1
                    if log.drop_success:
                        stats["by_source"][source]["successes"] += 1
                    
                    # 按类型统计
                    box_type = log.box_type.value
                    if box_type not in stats["by_type"]:
                        stats["by_type"][box_type] = {"attempts": 0, "successes": 0}
                    stats["by_type"][box_type]["attempts"] += 1
                    if log.drop_success:
                        stats["by_type"][box_type]["successes"] += 1
                
                return {
                    "success": True,
                    "user_id": user_id,
                    "period_days": days,
                    "statistics": stats,
                    "recent_logs": [self._format_drop_log(log) for log in logs[:20]]  # 最近20条
                }
                
        except Exception as e:
            logger.error(f"获取宝箱掉落历史失败 user_id={user_id}: {e}")  
            return {"error": "获取掉落历史失败", "code": "DROP_HISTORY_ERROR"}
    
    # ==================== 辅助方法 ====================
    
    async def _get_user_treasure_box(self, db: AsyncSession, user_id: int, box_id: str) -> Optional[UserTreasureBox]:
        """获取用户宝箱"""
        result = await db.execute(
            select(UserTreasureBox)
            .where(
                UserTreasureBox.id == box_id,
                UserTreasureBox.user_id == user_id
            )
        )
        return result.scalar_one_or_none()
    
    async def _get_today_drop_count(self, user_id: int, box_type: TreasureBoxType, drop_source: DropSource) -> int:
        """获取今日掉落次数"""
        try:
            today = datetime.now().date()
            cache_key = f"box_drops:{user_id}:{box_type.value}:{drop_source.value}:{today.isoformat()}"
            count = await redis_manager.get(cache_key)
            return int(count) if count else 0
        except Exception:
            return 0
    
    async def _log_drop_attempt(
        self,
        user_id: int,
        box_type: TreasureBoxType,
        drop_source: DropSource,
        roll: int,
        drop_rate: int,
        success: bool,
        reason: str,
        context: Dict[str, Any] = None
    ):
        """记录掉落尝试"""
        try:
            async for db in get_db():
                log = TreasureBoxDropLog(
                    user_id=user_id,
                    box_type=box_type,
                    drop_source=drop_source,
                    drop_roll=roll,
                    drop_rate=drop_rate,
                    drop_success=success,
                    session_id=context.get("session_id") if context else None,
                    city_id=context.get("city_id") if context else None,
                    level_type=context.get("level_type") if context else None
                )
                
                db.add(log)
                
                # 更新今日掉落计数缓存
                if success:
                    today = datetime.now().date()
                    cache_key = f"box_drops:{user_id}:{box_type.value}:{drop_source.value}:{today.isoformat()}"
                    await redis_manager.incr(cache_key)
                    await redis_manager.expire(cache_key, 86400)  # 24小时过期
                
        except Exception as e:
            logger.error(f"记录掉落尝试失败: {e}")
    
    async def _record_box_open(
        self,
        db: AsyncSession,
        user_id: int,
        box: UserTreasureBox,
        rewards: Dict[str, Any],
        use_ad: bool
    ):
        """记录宝箱开启"""
        try:
            # 计算奖励总价值
            total_value = rewards["stamina"] * 10  # 体力价值系数
            for items in [rewards["items"], rewards["artifacts"], rewards.get("rare_items", {})]:
                total_value += sum(items.values()) * 50  # 道具价值系数
            
            record = BoxOpenRecord(
                user_id=user_id,
                box_id=box.id,
                box_type=box.box_type,
                open_method="ad" if use_ad else "normal",
                stamina_gained=rewards["stamina"],
                items_gained=rewards["items"],
                artifacts_gained=rewards["artifacts"],
                total_value=total_value,
                was_lucky=bool(rewards.get("rare_items"))
            )
            
            db.add(record)
            
        except Exception as e:
            logger.error(f"记录宝箱开启失败: {e}")
    
    async def _update_user_box_statistics(
        self,
        user_id: int,
        boxes: List[Dict],
        action: str,
        is_ad: bool = False,
        rewards: Dict[str, Any] = None
    ):
        """更新用户宝箱统计"""
        try:
            async for db in get_db():
                # 获取或创建统计记录
                stats = await db.execute(
                    select(UserBoxStatistics).where(UserBoxStatistics.user_id == user_id)
                )
                stats = stats.scalar_one_or_none()
                
                if not stats:
                    stats = UserBoxStatistics(user_id=user_id)
                    db.add(stats)
                    await db.flush()
                
                # 更新统计
                for box_info in boxes:
                    box_type = box_info["box_type"]
                    if isinstance(box_type, str):
                        box_type = TreasureBoxType(box_type)
                    elif hasattr(box_type, 'value'):
                        box_type = TreasureBoxType(box_type.value)
                    
                    if action == "obtain":
                        stats.update_obtain_stats(box_type)
                    elif action == "open" and rewards:
                        stats.update_open_stats(box_type, is_ad, rewards)
                
                await db.commit()
                
        except Exception as e:
            logger.error(f"更新宝箱统计失败 user_id={user_id}: {e}")
    
    async def _get_user_box_stats(self, db: AsyncSession, user_id: int) -> Dict[str, Any]:
        """获取用户宝箱统计"""
        try:
            result = await db.execute(
                select(UserBoxStatistics).where(UserBoxStatistics.user_id == user_id)
            )
            stats = result.scalar_one_or_none()
            
            if not stats:
                return {
                    "total_obtained": 0,
                    "total_opened": 0,
                    "open_rate": 0,
                    "ad_rate": 0
                }
            
            return {
                "total_obtained": stats.total_boxes_obtained,
                "total_opened": stats.total_boxes_opened,
                "open_rate": stats.get_open_rate(),
                "ad_rate": stats.get_ad_rate(),
                "by_type": {
                    "copper": {
                        "obtained": stats.copper_boxes_obtained,
                        "opened": stats.copper_boxes_opened
                    },
                    "silver": {
                        "obtained": stats.silver_boxes_obtained,
                        "opened": stats.silver_boxes_opened
                    },
                    "gold": {
                        "obtained": stats.gold_boxes_obtained,
                        "opened": stats.gold_boxes_opened
                    }
                },
                "rewards": {
                    "total_stamina": stats.total_stamina_from_boxes,
                    "total_items": stats.total_items_from_boxes,
                    "total_artifacts": stats.total_artifacts_from_boxes
                }
            }
            
        except Exception as e:
            logger.error(f"获取宝箱统计失败: {e}")
            return {}
    
    async def _format_box_info(self, box: UserTreasureBox) -> Dict[str, Any]:
        """格式化宝箱信息"""
        return {
            "box_id": box.id,
            "box_type": box.box_type.value,
            "drop_source": box.drop_source.value,
            "status": box.status.value,
            "obtained_at": box.obtained_at.isoformat(),
            "opened_at": box.opened_at.isoformat() if box.opened_at else None,
            "expires_at": box.expires_at.isoformat() if box.expires_at else None,
            "time_until_expiry": box.get_time_until_expiry(),
            "is_expired": box.is_expired(),
            "can_open": box.can_open(),
            "was_ad_doubled": box.was_ad_doubled,
            "rewards": {
                "stamina": box.stamina_reward,
                "items": box.items_reward,
                "artifacts": box.artifacts_reward
            } if box.status != BoxStatus.PENDING else None
        }
    
    def _format_drop_log(self, log: TreasureBoxDropLog) -> Dict[str, Any]:
        """格式化掉落日志"""
        return {
            "box_type": log.box_type.value,
            "drop_source": log.drop_source.value,
            "drop_success": log.drop_success,
            "drop_roll": log.drop_roll,
            "drop_rate": log.drop_rate,
            "created_at": log.created_at.isoformat()
        }
    
    async def _cleanup_expired_boxes(self, db: AsyncSession, box_ids: List[str]):
        """清理过期宝箱"""
        try:
            await db.execute(
                delete(UserTreasureBox)
                .where(UserTreasureBox.id.in_(box_ids))
            )
            
            logger.info(f"🧹 清理过期宝箱: {len(box_ids)} 个")
            
        except Exception as e:
            logger.error(f"清理过期宝箱失败: {e}")
    
    async def _cache_user_boxes(self, user_id: int):
        """缓存用户宝箱信息"""
        try:
            cache_key = f"user_boxes:{user_id}"
            await redis_manager.delete(cache_key)  # 删除旧缓存，下次访问时重新生成
            
        except Exception as e:
            logger.error(f"缓存用户宝箱失败: {e}")


# 全局实例
treasure_box_service = TreasureBoxService()