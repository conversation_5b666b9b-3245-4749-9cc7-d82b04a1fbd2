# 游戏核心配置文件 - 完全符合PRD《千亿像素城市寻宝》要求

# 货币系统配置 - 符合PRD要求
currency:
  initial_gold: 200        # PRD要求：初始金币200（符合等级1基础配置）
  initial_diamond: 20      # PRD要求：初始钻石20（符合等级1基础配置）
  initial_ammunition: 50   # PRD要求：初始弹药50
  gold_cap: 999999999      # 金币上限
  diamond_cap: 999999      # 钻石上限
  initial_stamina: 120     # PRD要求：初始体力120点
  max_stamina: 120         # PRD要求：最大体力120点

# 体力系统配置 - 严格按照PRD要求
stamina_system:
  max_stamina: 120                    # PRD要求：最大体力120点
  recovery_rate_minutes: 3            # PRD要求：每3分钟恢复1点体力
  consumption_rates:
    catch_thief: 1                    # PRD要求：抓捕小偷消耗1点体力
    clean_garbage: 1                  # PRD要求：清理垃圾消耗1点体力
    cultural_quiz: 5                  # PRD要求：古迹问答消耗5点体力
  efficiency_thresholds:
    normal_efficiency: 30             # PRD要求：体力≥30时100%效率
    reduced_efficiency: 1             # PRD要求：体力1-29时75%效率
    no_play: 0                        # PRD要求：体力0时无法游戏
  ad_recovery:
    stamina_amount: 30                # PRD要求：广告恢复30点体力
    hourly_limit: 3                   # PRD要求：每小时3次
  special_recovery:
    rebel_thief_chance: 0.1           # PRD要求：10%概率遇到叛变小偷
    rebel_thief_stamina: 10           # PRD要求：叛变小偷恢复10点体力
    level_up_full_recovery: true      # PRD要求：升级时完全恢复体力

# 经验系统配置 - 严格按照PRD要求
experience_system:
  # 基础经验值获取 - 完全按照PRD表格配置
  base_experience:
    catch_thief: 12                   # PRD要求：抓捕小偷12点经验
    clean_garbage: 8                  # PRD要求：清理垃圾8点经验
    cultural_quiz: 35                 # PRD要求：古迹问答35点经验
    cultural_artifact: 25             # PRD要求：文化图鉴25点经验
    level_complete: 200               # PRD要求：关卡通关200点经验
    daily_task_range: [50, 800]      # PRD要求：完成任务50-800点经验
    passive_income_range: [2, 60]    # PRD要求：被动收益2-60点/分钟

  # 体力不足时经验减少 - PRD要求
  low_stamina_penalty:
    threshold: 30                     # 体力<30时触发
    reduction_rate: 0.25              # 减少25%经验

  # 广告双倍经验 - PRD要求
  ad_double_experience:
    cultural_quiz: true               # 古迹问答可广告双倍
    cultural_artifact: true           # 文化图鉴可广告双倍
    daily_task: true                  # 任务奖励可广告双倍
    passive_income: true              # 被动收益可广告双倍
    level_up_boost: true              # 升级加速可广告+10%

  # 守护者等级系统 - 严格按照PRD表格
  guardian_levels:
    # 等级1-10的具体配置
    1:
      required_exp: 100
      cumulative_exp: 100
      appearance: "初级守卫外观"
      passive_income: 2               # 2经验/分钟
      unlocks: ["基础功能"]
    2:
      required_exp: 150
      cumulative_exp: 250
      appearance: "初级守卫外观"
      passive_income: 3               # 3经验/分钟
      unlocks: ["基础任务"]
    3:
      required_exp: 250
      cumulative_exp: 500
      appearance: "初级守卫外观"
      passive_income: 5               # 5经验/分钟
      unlocks: ["更多任务"]
    4:
      required_exp: 500
      cumulative_exp: 1000
      appearance: "中级守护者外观"
      passive_income: 8               # 8经验/分钟
      unlocks: ["高级任务"]
    5:
      required_exp: 1000
      cumulative_exp: 2000
      appearance: "中级守护者外观"
      passive_income: 12              # 12经验/分钟
      unlocks: ["特殊道具"]
    6:
      required_exp: 2000
      cumulative_exp: 4000
      appearance: "中级守护者外观"
      passive_income: 15              # 15经验/分钟
      unlocks: ["装备系统"]
    7:
      required_exp: 4000
      cumulative_exp: 8000
      appearance: "高级执法者外观"
      passive_income: 20              # 20经验/分钟
      unlocks: ["技能系统"]
    8:
      required_exp: 7000
      cumulative_exp: 15000
      appearance: "高级执法者外观"
      passive_income: 30              # 30经验/分钟
      unlocks: ["专属特效"]
    9:
      required_exp: 10000
      cumulative_exp: 25000
      appearance: "高级执法者外观"
      passive_income: 35              # 35经验/分钟
      unlocks: ["竞技功能"]
    10:
      required_exp: 15000
      cumulative_exp: 40000
      appearance: "传奇守护者外观"
      passive_income: 40              # 40经验/分钟
      unlocks: ["全部权限"]

  # 10级以上的配置
  level_10_plus:
    exp_per_level: 15000              # PRD要求：10级+每级15000点经验
    passive_income_range: [40, 60]   # PRD要求：40-60经验/分钟
    appearance: "传奇守护者外观"
    unlocks: ["全部权限"]

# 宝箱系统配置 - 严格按照PRD要求
treasure_box_system:
  # 宝箱掉落率配置 - 完全按照PRD表格
  drop_rates:
    catch_thief:
      box_type: "copper"
      drop_rate: 0.06                 # PRD要求：6%掉落率
    clean_garbage:
      box_type: "copper"
      drop_rate: 0.03                 # PRD要求：3%掉落率
    cultural_quiz:
      box_type: "silver"
      drop_rate: 0.15                 # PRD要求：15%掉落率
    level_complete:
      box_type: "gold"
      drop_rate: 1.0                  # PRD要求：100%必掉

  # 宝箱奖励配置 - 完全按照PRD表格
  box_rewards:
    copper:
      free_rewards:
        stamina: 5                    # PRD要求：体力+5
        items: 1                      # PRD要求：道具×1
      ad_rewards:
        stamina: 10                   # PRD要求：体力+10
        items: 2                      # PRD要求：道具×2
      daily_expected: [4, 5]          # PRD要求：每日预期4-5个
    silver:
      free_rewards:
        artifact: 1                   # PRD要求：图鉴×1
        stamina: 10                   # PRD要求：体力+10
      ad_rewards:
        artifact: 2                   # PRD要求：图鉴×2
        stamina: 20                   # PRD要求：体力+20
      daily_expected: [1, 2]          # PRD要求：每日预期1-2个
    gold:
      free_rewards:
        rare_artifact: 1              # PRD要求：稀有图鉴×1
        items: 1                      # PRD要求：道具×1
      ad_rewards:
        rare_artifact: 2              # PRD要求：稀有图鉴×2
        items: 2                      # PRD要求：道具×2
      daily_expected: [0, 1]          # PRD要求：每日预期0-1个

  # 道具系统价值表 - 按照PRD配置
  items:
    magnifier:
      name: "放大镜"
      effect: "高亮未发现目标3秒"
      sources: ["copper", "silver"]
      usage_limit: "每关卡1次"
      time_value: "节省10-30秒"
      rarity: "常见"
    radar:
      name: "雷达"
      effect: "显示最近目标方向指示"
      sources: ["silver", "gold"]
      usage_limit: "每关卡1次"
      time_value: "节省30-60秒"
      rarity: "稀有"
    stamina_potion:
      name: "体力药水"
      effect: "立即恢复20体力"
      sources: ["copper", "silver", "gold"]
      usage_limit: "无限制"
      time_value: "等价1小时等待"
      rarity: "实用"

# BOSS血量系统配置 - 严格按照PRD要求
boss_health_system:
  # BOSS血量机制 - 完全按照PRD表格
  phases:
    initial:
      health_percentage: 100
      trigger: "进入关卡"
      boss_behavior: "嘲讽对话，展示满血状态"
      player_goal: "开始收集小偷和垃圾"
    middle:
      health_percentage: 50
      trigger: "收集50%进度"
      boss_behavior: "愤怒对话，血量下降"
      player_goal: "继续收集降低BOSS血量"
    final:
      health_percentage: 20
      trigger: "收集80%进度"
      boss_behavior: "求饶对话，血量危险"
      player_goal: "完成最后收集任务"
    defeated:
      health_percentage: 0
      trigger: "收集100%进度"
      boss_behavior: "失败对话，血量清零，城市恢复"
      player_goal: "获得通关奖励+金宝箱"

  # BOSS血量与收集关系 - 完全按照PRD表格
  damage_mapping:
    catch_thief:
      health_damage: 2                # PRD要求：每个小偷-2%血量
      visual_feedback: "BOSS头像闪红光"
      treasure_box_chance: 0.06       # PRD要求：铜宝箱掉落6%
    clean_garbage:
      health_damage: 1                # PRD要求：每个垃圾-1%血量
      visual_feedback: "城市恢复效果"
      treasure_box_chance: 0.03       # PRD要求：铜宝箱掉落3%
    cultural_quiz:
      health_damage: 10               # PRD要求：每题正确-10%血量
      visual_feedback: "BOSS大幅后退"
      treasure_box_chance: 0.15       # PRD要求：银宝箱掉落15%
    level_complete:
      health_damage: 100              # 血量清零
      visual_feedback: "城市完全恢复，BOSS消失"
      rewards:
        gold_box: true                # PRD要求：金宝箱必掉
        experience: 200               # PRD要求：+200经验

  # BOSS对话系统 - 完全按照PRD要求
  dialogues:
    arrogant_phase:
      health_range: [80, 100]         # 100%-80%血量
      dialogue: "你这个失败者，永远无法阻止我们！"
    angry_phase:
      health_range: [50, 80]          # 80%-50%血量
      dialogue: "可恶！我要派出更多的手下！"
    begging_phase:
      health_range: [20, 50]          # 50%-20%血量
      dialogue: "求你手下留情，我们会改邪归正的！"
    defeat_phase:
      health_range: [0, 20]           # 20%-0%血量
      dialogue: "算你厉害，我们先撤，但我还会回来的！"

# 每日任务系统配置 - 严格按照PRD要求
daily_task_system:
  # 任务类别配置 - 完全按照PRD表格
  task_categories:
    catch_thief:
      name: "抓捕任务"
      description: "抓捕小偷"
      targets: [15, 30, 60]            # PRD要求：15/30/60个
      experience_rewards: [50, 120, 300] # PRD要求：50/120/300点
      estimated_times: [15, 30, 60]    # PRD要求：15/30/60分钟
      efficiency: [3.3, 4, 5]          # PRD要求：3.3/4/5点/分钟
      difficulty_levels: [1, 2, 3]
    clean_garbage:
      name: "清理任务"
      description: "清除垃圾"
      targets: [20, 40, 80]            # PRD要求：20/40/80个
      experience_rewards: [40, 100, 250] # PRD要求：40/100/250点
      estimated_times: [20, 40, 80]    # PRD要求：20/40/80分钟
      efficiency: [2, 2.5, 3.1]        # PRD要求：2/2.5/3.1点/分钟
      difficulty_levels: [1, 2, 3]
    cultural_learning:
      name: "学习任务"
      description: "文化学习"
      targets: [2, 5, 10]              # PRD要求：2/5/10个
      experience_rewards: [80, 200, 500] # PRD要求：80/200/500点
      estimated_times: [10, 25, 50]    # PRD要求：10/25/50分钟
      efficiency: [8, 8, 10]           # PRD要求：8/8/10点/分钟
      difficulty_levels: [1, 2, 3]
    perfect_clear:
      name: "通关任务"
      description: "完美通关"
      targets: [1, 3, 5]               # PRD要求：1/3/5关
      experience_rewards: [100, 300, 800] # PRD要求：100/300/800点
      estimated_times: [30, 90, 150]   # PRD要求：30/90/150分钟
      efficiency: [3.3, 3.3, 5.3]     # PRD要求：3.3/3.3/5.3点/分钟
      difficulty_levels: [1, 2, 3]

  # 任务状态管理 - 按照PRD要求
  task_management:
    progress_tracking: true           # 实时更新完成进度
    base_rewards: true                # 基础奖励
    ad_double_option: true            # 广告双倍选项
    daily_reset_time: "00:00"         # 每日0点自动重置
    incomplete_reminder: true         # 未完成任务的提醒机制
    ad_limit_per_task: 1              # 每个任务1次广告双倍

  # 任务模板配置 - 根据PRD优化
  task_templates:
    - id: "catch_thief_easy"
      task_code: "catch_thief_easy"
      task_type: "catch_thief"
      name: "新手守卫"
      description: "抓捕15个小偷，保护城市安全"
      target_amounts: [15, 30, 60]
      reward_experiences: [50, 120, 300]
      estimated_times: [15, 30, 60]
      difficulty_level: 1
      required_level: 1
      is_active: true
    - id: "clean_rubbish_easy"
      task_code: "clean_rubbish_easy"
      task_type: "clean_rubbish"
      name: "环保新人"
      description: "清理20个垃圾，净化城市环境"
      target_amounts: [20, 40, 80]
      reward_experiences: [40, 100, 250]
      estimated_times: [20, 40, 80]
      difficulty_level: 1
      required_level: 1
      is_active: true
    - id: "cultural_quiz_easy"
      task_code: "cultural_quiz_easy"
      task_type: "cultural_quiz"
      name: "文化学徒"
      description: "完成2次文化学习，传承文化知识"
      target_amounts: [2, 5, 10]
      reward_experiences: [80, 200, 500]
      estimated_times: [10, 25, 50]
      difficulty_level: 1
      required_level: 1
      is_active: true
    - id: "perfect_clear_easy"
      task_code: "perfect_clear_easy"
      task_type: "perfect_clear"
      name: "通关新手"
      description: "完美通关1个关卡，展现守护者实力"
      target_amounts: [1, 3, 5]
      reward_experiences: [100, 300, 800]
      estimated_times: [30, 90, 150]
      difficulty_level: 1
      required_level: 1
      is_active: true

# 登录奖励配置
login_rewards:
  cycle_days: 7
  rewards:
    - day: 1
      gold: 100
      exp: 10
    - day: 2
      gold: 200
      exp: 20
    - day: 3
      gold: 300
      diamond: 1
      exp: 30
    - day: 4
      gold: 400
      exp: 40
    - day: 5
      gold: 500
      diamond: 2
      exp: 50
    - day: 6
      gold: 600
      exp: 60
    - day: 7
      gold: 1000
      diamond: 5
      exp: 100

# 广告配置
ads:
  daily_limits:
    gold: 5
    diamond: 2
    double_reward: 3
  rewards:
    amount: 20
    gold:
      amount: 100
    diamond:
      amount: 2
    double_reward:
      multiplier: 2

# 转盘抽奖配置
lottery_wheel:
  daily_limit: 300
  prizes:
    - id: 1
      icon: "💰"
      text: "200金币"
      type: "gold"
      amount: 200
      color: "#FFD700"
      weight: 20
    - id: 2
      icon: "💎"
      text: "5钻石"
      type: "diamond"
      amount: 5
      color: "#87CEEB"
      weight: 10
    - id: 3
      icon: "💰"
      text: "250金币"
      type: "gold"
      amount: 250
      color: "#FFA500"
      weight: 10
    - id: 4
      icon: "💰"
      text: "150金币"
      type: "gold"
      amount: 150
      color: "#4169E1"
      weight: 20
    - id: 5
      icon: "💰"
      text: "15钻石"
      type: "diamond"
      amount: 15
      color: "#9370DB"
      weight: 10 
    - id: 6
      icon: "💰"
      text: "400金币"
      type: "gold"
      amount: 400
      color: "#9370DB"
      weight: 10
    - id: 7
      icon: "💰"
      text: "300金币"
      type: "gold"
      amount: 300
      color: "#FFD700"
      weight: 10
    - id: 8
      icon: "💎"
      text: "10钻石"
      type: "diamond"
      amount: 10
      color: "#87CEEB"
      weight: 10

# 被动收益系统配置
passive_income:
  base_gold_per_hour: 100
  base_diamond_per_hour: 2
  max_accumulation_hours: 24
  guardian_level_bonus_rate: 0.05  # 每级增加5%

# 守护等级系统配置
guardian_system:
  experience_per_level_formula: "10 * level"  # 每级所需经验
  upgrade_cost:
    gold_formula: "1000 * (1.15 ** level)"
    diamond_formula: "20 * (1.08 ** level)"
  artifact_experience:
    bronze: 1
    silver: 5
    gold: 20
  level_bonuses:
    - level: 10
      effect: "auto_collect_passive"
      description: "自动领取被动收益"
    - level: 20
      effect: "extend_accumulation_48h"
      description: "被动收益积累上限延长至48小时"
    - level: 30
      effect: "attack_cooldown_reduction_10"
      description: "攻击冷却时间减少10%"
    - level: 50
      effect: "double_ad_rewards"
      description: "所有广告奖励翻倍"

# 弹药系统配置
ammunition:
  craft_ratio: 1  # 多少个收集物合成1发弹药（1个小偷或垃圾 = 1发弹药）
  max_ammunition: 999
  consumption_bonus:
    threshold: 10  # 使用10发弹药时获得加成
    multiplier: 1.2  # 蓄力加成倍数

# 大炮类型系统（完整设计）
cannon_types:
  wooden:
    id: "wooden_cannon"
    name: "木质大炮"
    name_translations: ''
    description: "简单的木制大炮，威力有限但易于操作"
    description_translations: ''
    unlock_condition: "always_unlocked"  # 始终解锁
    unlock_cost: 0
    purchase_cost: 0  # 免费获得
    appearance: "wooden_barrel"
    icon: "/img/page_icons/cannon-1.png"
    model: "/models/cannons/wooden_cannon.glb"
    damage_bonus: 1.0
    crit_bonus: 1.0
    accuracy_bonus: 1.0
    sound_effects:
      fire: "/sounds/cannon_fire_wooden.mp3"
      reload: "/sounds/cannon_reload_wooden.mp3"
    particle_effects:
      muzzle_flash: "wooden_muzzle_flash"
      explosion: "wooden_explosion"
    upgrade_stats:
      damage:
        min_value: 10      # 最小基础伤害
        max_value: 50      # 最大基础伤害 (20次升级)
        initial_value: 10  # 初始值
        upgrade_cost_gold: 0      # 每次升级消耗金币
        upgrade_cost_diamond: 1   # 每次升级消耗钻石 (总共20钻石满级)
        upgrade_increment: 2      # 每次升级增加的数值
      crit_rate:
        min_value: 5       # 最小暴击率(%)
        max_value: 25      # 最大暴击率(%) (20次升级)
        initial_value: 5   # 初始值
        upgrade_cost_gold: 100    # 每次升级消耗金币 (总共2000金币满级)
        upgrade_cost_diamond: 0   # 每次升级消耗钻石
        upgrade_increment: 1      # 每次升级增加的数值
      accuracy:
        min_value: 85      # 最小命中率(%)
        max_value: 95      # 最大命中率(%) (10次升级)
        initial_value: 85  # 初始值
        upgrade_cost_gold: 150    # 每次升级消耗金币 (总共1500金币满级)
        upgrade_cost_diamond: 0   # 每次升级消耗钻石
        upgrade_increment: 1      # 每次升级增加的数值
  
  steel:
    id: "steel_cannon"
    name: "粗钢大炮"
    name_translations: ''
    description: "坚固的钢制大炮，威力和精度都有显著提升"
    description_translations: ''
    unlock_condition: "always_unlocked"  # 始终解锁
    unlock_cost: 0
    purchase_cost: 50   # 50钻石购买
    appearance: "steel_barrel"
    icon: "/img/page_icons/cannon-2.png"
    model: "/models/cannons/steel_cannon.glb"
    damage_bonus: 1.4
    crit_bonus: 1.3
    accuracy_bonus: 1.2
    sound_effects:
      fire: "/sounds/cannon_fire_steel.mp3"
      reload: "/sounds/cannon_reload_steel.mp3"
    particle_effects:
      muzzle_flash: "steel_muzzle_flash"
      explosion: "steel_explosion"
    upgrade_stats:
      damage:
        min_value: 25      # 最小基础伤害
        max_value: 100     # 最大基础伤害 (25次升级)
        initial_value: 25  # 初始值
        upgrade_cost_gold: 0      # 每次升级消耗金币
        upgrade_cost_diamond: 3   # 每次升级消耗钻石 (总共75钻石满级)
        upgrade_increment: 3      # 每次升级增加的数值
      crit_rate:
        min_value: 10      # 最小暴击率(%)
        max_value: 35      # 最大暴击率(%) (25次升级)
        initial_value: 10  # 初始值
        upgrade_cost_gold: 200    # 每次升级消耗金币 (总共5000金币满级)
        upgrade_cost_diamond: 0   # 每次升级消耗钻石
        upgrade_increment: 1      # 每次升级增加的数值
      accuracy:
        min_value: 88      # 最小命中率(%)
        max_value: 98      # 最大命中率(%) (10次升级)
        initial_value: 88  # 初始值
        upgrade_cost_gold: 300    # 每次升级消耗金币 (总共3000金币满级)
        upgrade_cost_diamond: 0   # 每次升级消耗钻石
        upgrade_increment: 1      # 每次升级增加的数值
  
  punk:
    id: "punk_cannon"
    name: "朋克重炮"
    name_translations: ''
    description: "蒸汽朋克风格的重型大炮，火力凶猛"
    description_translations: ''
    unlock_condition: "always_unlocked"  # 始终解锁
    unlock_cost: 0
    purchase_cost: 150  # 150钻石购买
    appearance: "punk_heavy"
    icon: "/img/page_icons/cannon-3.png"
    model: "/models/cannons/punk_cannon.glb"
    damage_bonus: 1.8
    crit_bonus: 1.6
    accuracy_bonus: 1.4
    sound_effects:
      fire: "/sounds/cannon_fire_punk.mp3"
      reload: "/sounds/cannon_reload_punk.mp3"
    particle_effects:
      muzzle_flash: "punk_muzzle_flash"
      explosion: "punk_explosion"
    upgrade_stats:
      damage:
        min_value: 50      # 最小基础伤害
        max_value: 200     # 最大基础伤害 (30次升级)
        initial_value: 50  # 初始值
        upgrade_cost_gold: 0      # 每次升级消耗金币
        upgrade_cost_diamond: 8   # 每次升级消耗钻石 (总共240钻石满级)
        upgrade_increment: 5      # 每次升级增加的数值
      crit_rate:
        min_value: 15      # 最小暴击率(%)
        max_value: 45      # 最大暴击率(%) (30次升级)
        initial_value: 15  # 初始值
        upgrade_cost_gold: 400    # 每次升级消耗金币 (总共12000金币满级)
        upgrade_cost_diamond: 0   # 每次升级消耗钻石
        upgrade_increment: 1      # 每次升级增加的数值
      accuracy:
        min_value: 92      # 最小命中率(%)
        max_value: 99      # 最大命中率(%) (7次升级)
        initial_value: 92  # 初始值
        upgrade_cost_gold: 600    # 每次升级消耗金币 (总共4200金币满级)
        upgrade_cost_diamond: 0   # 每次升级消耗钻石
        upgrade_increment: 1      # 每次升级增加的数值
  
  laser:
    id: "laser_cannon"
    name: "镭射光炮"
    name_translations: ''
    description: "高科技激光武器，精准度极高，威力惊人"
   
    unlock_condition: "always_unlocked"  # 始终解锁
    unlock_cost: 0
    purchase_cost: 300  # 300钻石购买
    appearance: "laser_beam"
    icon: "/img/page_icons/cannon-4.png"
    model: "/models/cannons/laser_cannon.glb"
    damage_bonus: 2.2
    crit_bonus: 1.8
    accuracy_bonus: 1.6
    sound_effects:
      fire: "/sounds/cannon_fire_laser.mp3"
      reload: "/sounds/cannon_reload_laser.mp3"
    particle_effects:
      muzzle_flash: "laser_muzzle_flash"
      explosion: "laser_explosion"
    upgrade_stats:
      damage:
        min_value: 80      # 最小基础伤害
        max_value: 320     # 最大基础伤害 (30次升级)
        initial_value: 80  # 初始值
        upgrade_cost_gold: 0      # 每次升级消耗金币
        upgrade_cost_diamond: 15  # 每次升级消耗钻石 (总共450钻石满级)
        upgrade_increment: 8      # 每次升级增加的数值
      crit_rate:
        min_value: 20      # 最小暴击率(%)
        max_value: 50      # 最大暴击率(%) (30次升级)
        initial_value: 20  # 初始值
        upgrade_cost_gold: 800    # 每次升级消耗金币 (总共24000金币满级)
        upgrade_cost_diamond: 0   # 每次升级消耗钻石
        upgrade_increment: 1      # 每次升级增加的数值
      accuracy:
        min_value: 95      # 最小命中率(%)
        max_value: 100     # 最大命中率(%) (5次升级)
        initial_value: 95  # 初始值
        upgrade_cost_gold: 1000   # 每次升级消耗金币 (总共5000金币满级)
        upgrade_cost_diamond: 0   # 每次升级消耗钻石
        upgrade_increment: 1      # 每次升级增加的数值

  titan:
    id: "titan_cannon"
    name: "泰坦重炮"
    name_translations: ''
    description: "终极重型武器，拥有毁天灭地的恐怖威力"
    description_translations: ''
    unlock_condition: "always_unlocked"  # 始终解锁
    unlock_cost: 0
    purchase_cost: 500   # 500钻石购买
    appearance: "titan_massive"
    icon: "/img/page_icons/cannon-5.png"
    model: "/models/cannons/titan_cannon.glb"
    damage_bonus: 3.0
    crit_bonus: 2.5
    accuracy_bonus: 1.8
    sound_effects:
      fire: "/sounds/cannon_fire_titan.mp3"
      reload: "/sounds/cannon_reload_titan.mp3"
    particle_effects:
      muzzle_flash: "titan_muzzle_flash"
      explosion: "titan_explosion"
    upgrade_stats:
      damage:
        min_value: 120     # 最小基础伤害
        max_value: 480     # 最大基础伤害 (36次升级)
        initial_value: 120 # 初始值
        upgrade_cost_gold: 0      # 每次升级消耗金币
        upgrade_cost_diamond: 25  # 每次升级消耗钻石 (总共900钻石满级)
        upgrade_increment: 10     # 每次升级增加的数值
      crit_rate:
        min_value: 25      # 最小暴击率(%)
        max_value: 60      # 最大暴击率(%) (35次升级)
        initial_value: 25  # 初始值
        upgrade_cost_gold: 1200   # 每次升级消耗金币 (总共42000金币满级)
        upgrade_cost_diamond: 0   # 每次升级消耗钻石
        upgrade_increment: 1      # 每次升级增加的数值
      accuracy:
        min_value: 98      # 最小命中率(%)
        max_value: 100     # 最大命中率(%) (2次升级)
        initial_value: 98  # 初始值
        upgrade_cost_gold: 1500   # 每次升级消耗金币 (总共3000金币满级)
        upgrade_cost_diamond: 0   # 每次升级消耗钻石
        upgrade_increment: 1      # 每次升级增加的数值

# 大炮切换和购买配置
cannon_system:
  # 默认大炮
  default_cannon: "wooden_cannon"

  # 大炮解锁条件检查
  unlock_conditions:
    level_based: false        # 不再基于等级
    total_level_based: false  # 不再基于总等级
    achievement_based: false

  # 购买限制
  purchase_limits:
    daily_diamond_purchases: 10
    require_previous_unlock: false  # 所有大炮都可直接购买

  # 大炮切换
  switching:
    animation_duration: 800  # 毫秒
    cooldown_period: 2000   # 切换冷却时间(毫秒)
    sound_effect: "/sounds/cannon_switch.mp3"

  # UI配置
  ui_config:
    carousel_autoplay: false
    swipe_threshold: 50      # 滑动阈值(px)
    show_locked_cannons: true
    preview_mode: true

  # 伤害计算配置
  damage_calculation:
    # 最终伤害 = (基础伤害 + 大炮类型加成) * 暴击倍数 * 命中率
    base_damage_weight: 1.0      # 基础伤害权重
    crit_damage_multiplier: 2.0  # 暴击伤害倍数
    accuracy_damage_factor: 1.0  # 命中率对伤害的影响因子
    # 总伤害计算公式: final_damage = base_damage * cannon_bonus * (1 + crit_rate/100 * crit_multiplier) * (accuracy/100)

# AI交互配置
ai_interactions:
  thief_diary:
    enabled: true
    max_tokens: 200
    temperature: 0.8
  boss_clue:
    enabled: true
    max_tokens: 150
    temperature: 0.7
  artifact_story:
    enabled: true
    max_tokens: 300
    temperature: 0.9 